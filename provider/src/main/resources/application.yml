dubbo:
  application:
    name: demo-provider # 服务名
  registry:
    address: zookeeper://*************:2181 # 注册中心地址
    check: false
  protocol:
    name: dubbo # 指定通信协议
    port: 20880 # 通信端口  这里指的是与消费者间的通信协议与端口
  provider:
    timeout: 100000 # 配置全局调用服务超时时间，dubbo默认是1s，肯定不够用呀
    retries: 3 # 重试3次
    delay: -1
    contextpath: /luoluo
  config-center:

server:
  port: 7894
shenyu:
  client:
    registerType: http
    serverLists: http://*************:9095
    props:
      contextPath: /luoluo2
      appName: demo-provider
      sessionTimeout: 5000
      connectionTimeout: 2000