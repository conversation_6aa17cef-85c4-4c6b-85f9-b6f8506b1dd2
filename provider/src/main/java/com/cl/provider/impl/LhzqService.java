package com.newland.streamboss.streamCreateMontnetCdr.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.db.Db;
import com.newland.boss.nlstreamboss.streamcompoment.domain.infoobject.*;
import com.newland.boss.nlstreamboss.streamcompoment.domain.util.CacheNameDef;
import com.newland.boss.nlstreamboss.streamcompoment.domain.workobject.AcctbkDetail;
import com.newland.boss.nlstreamboss.streamcompoment.domain.workobject.AcctbkInfo;
import com.newland.computer.boss.bossbiz.app.appprocess.appcommon.util.AppException;
import com.newland.computer.boss.bossbiz.app.appprocess.appcommon.util.DateTool;
import com.newland.computer.boss.bossbiz.app.appprocess.appcommon.util.FileTool;
import com.newland.sri.ccp.common.util.DateUtils;
import com.newland.sri.ccp.logmgr.Log;
import com.newland.sri.ccp.logmgr.LogFactory;
import com.newland.sri.ccp.logmgr.LogProperty;
import com.newland.streamboss.biz_createMontnetCdr.api.ICreateMonManage;
import com.newland.streamboss.biz_createMontnetCdr.api.IUserInfoManage;
import com.newland.streamboss.biz_createMontnetCdr.bo.*;
import com.newland.streamboss.streamCreateMontnetCdr.config.Montnetconfig;
import com.newland.streamboss.streamCreateMontnetCdr.entity.DbResultInfo;
import com.newland.streamboss.streamCreateMontnetCdr.entity.ParamBean;
import com.newland.streamboss.streamCreateMontnetCdr.process.StreamCreateMontnetCdrProcess;
import com.newland.streamboss.streamCreateMontnetCdr.utils.MqUtil;
import com.newland.streamboss.utils.utils.LogUtil;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.FileNotFoundException;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @program: SourceCode
 * @description:
 * @author: luoting
 * @create: 2022-02-17 14:51
 **/
@Service
public class LhzqService {

    private static String TAG = "LhzqService======";


    @Autowired
    IUserInfoManage iUserInfoManage;


    @Autowired
    ICreateMonManage iCreateMonManage;
    public static final char COMMA_STR = ',';
    public List<CngUserSpData> isLHZQMcdr(List<CngUserSpData> dtUserSPlistONE, DbResultInfo dbResultInfo) {

        LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "判断是否是包天计费~" + dtUserSPlistONE.size());


        ArrayList<CngUserSpData> tempList = new ArrayList<>();

        Iterator<CngUserSpData> iterator = dtUserSPlistONE.iterator();
        while (iterator.hasNext()) {
            CngUserSpData userSpData = iterator.next();
            List<CngExdSpOperDefAllData> spOperDefAllDataList = dbResultInfo.getMapSpOperInfo().get(userSpData.getsSpID() + userSpData.getsSpBizID());


            LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "CngUserSpData：" + userSpData);
            LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "mapSpOperInfo：" + ArrayUtils.toString(spOperDefAllDataList));


            if (spOperDefAllDataList != null && !spOperDefAllDataList.isEmpty()) {
                for (CngExdSpOperDefAllData spOperDefAllData : spOperDefAllDataList) {
                    if ("7".equals(spOperDefAllData.getStrBILLFLAG())) {
                        //如果是动态月，还需要计算小周期
                        int cycleTime = 0;
                        if ("2".equals(spOperDefAllData.getBydayMode())) {
                            cycleTime = calCycleTime(userSpData.getsStartDate());

                            //如果小周期次数大于服务频率 ， 不收费
                            if (cycleTime > Integer.parseInt(spOperDefAllData.getSeverType())) {
                                //因为是灵活周期计费 所以直接删除 不走正常包月费判断
                                iterator.remove();
                                continue;
                            }
                        }

                        //根据订购实例、用户、sp_code、oper_code去流水表中进行排重
                        List<CngDcdrDetailData> dcdrDetailDataList = dbResultInfo.getDcdrDetailDataMap().get(userSpData.getsUserID());

                        LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "根据订购实例、用户、sp_code、oper_code去流水表中进行排重~dcdrDetailDataList.size:" + (dcdrDetailDataList == null ? 0 : dcdrDetailDataList.size()));


                        boolean addFlag = false;
                        if (dcdrDetailDataList != null) {
                            boolean findFlag = false;
                            for (CngDcdrDetailData dcdrDetailData : dcdrDetailDataList) {
                                if (userSpData.getsUserID() == dcdrDetailData.getUserId() &&
                                        userSpData.getOrderId().equals(dcdrDetailData.getOrderId())
                                        && userSpData.getsSpID().equals(dcdrDetailData.getSpCode())
                                        && userSpData.getsSpBizID().equals(dcdrDetailData.getOperCode())) {
                                    //如果是动态月，还需要判断小周期是否一致
                                    if ("2".equals(spOperDefAllData.getBydayMode()) && dcdrDetailData.getCycleTimes() < cycleTime) {
                                        LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "动态月小周期不一致！dcdrDetailData.getCycleTimes():" + dcdrDetailData.getCycleTimes() + "  cycleTime:" + cycleTime);

                                        continue;
                                    }


                                    LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "dcdrDetailDataMap有记录，此包天费用已经收过费！" + userSpData);

                                    findFlag = true;
                                    break;
                                }
                            }

                            if (!findFlag) {

                                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "dcdrDetailDataMap没有查到，此包天费用有效！" + userSpData);

                                addFlag = true;
                            }

                        } else {
                            addFlag = true;

                            LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "dcdrDetailDataMap没有查到，此包天费用有效！" + userSpData);

                        }

                        //动态月扣费优化，第一个周期之后的周期每次扣费保证在8点到18点之间，防止 夜里扣费导致停机。
                        if (addFlag) {
                            if ("2".equals(spOperDefAllData.getBydayMode())) {
                                if (cycleTime == 1 || (cycleTime > 1 && LocalDateTime.now().getHour() >= 8 && LocalDateTime.now().getHour() <=Montnetconfig.lastDealHour)) {
                                    userSpData.setCycleTimes(cycleTime);
                                    tempList.add(userSpData);
                                } else {

                                    LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "动态月扣费优化，第一个周期之后的周期每次扣费保证在8点到18点之间，防止夜里扣费导致停机！所以此动态月暂时不收费~！" + userSpData);

                                }
                            } else {
                                tempList.add(userSpData);
                            }
                        }


                        LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "因为是灵活周期计费 所以直接删除 不走正常包月费判断!" + userSpData);

                        //因为是灵活周期计费 所以直接删除 不走正常包月费判断
                        iterator.remove();
                    }
                }
            }
        }


        LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "判断结束，包天计费size~" + tempList.size());


        return tempList;
    }


    /**
     * @return int
     * <AUTHOR>
     * @Description 计算动态月周期
     * @Date 11:03 2021/5/27
     * @Param [time]
     */
    private int calCycleTime(String time) {

        if (time.length() < 8) {
            return 0;
        }
        LocalDate date = LocalDate.of(Integer.parseInt(time.substring(0, 4)), Integer.parseInt(time.substring(4, 6)), Integer.parseInt(time.substring(6, 8)));
        LocalDate now = LocalDate.now();
        if (now.compareTo(date) < 0) {
            return 0;
        }
        int i = 1;
        while (true) {
            if (now.compareTo(date.plusMonths(i)) < 0) {

                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "计算出的动态月周期是:" + i);

                return i;
            }
            i++;
        }
    }


    public void doProcess(boolean bCharge, List<CngUserSpData> dtUserSPlistONEAll, List<String> dtOneUserMcdrList, DbResultInfo dbResultInfo, List<Object[]> message) {
        int row = 0;
        int loop2 = 0;
        int col = 0;

        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "工作线程接收到的消息list.size():" + dtUserSPlistONEAll.size() + "]");

        for (row = 0; row < dtUserSPlistONEAll.size(); row++) {
            //initParams();
            List<CngUserSpData> dtUserSPlistONE = new ArrayList<CngUserSpData>();
            dtUserSPlistONE.add(dtUserSPlistONEAll.get(row));
            loop2 = row;

            for (col = (row + 1); col < dtUserSPlistONEAll.size(); col++) {
                if (dtUserSPlistONEAll.get(row).equalsAll(dtUserSPlistONEAll.get(col))) {
                    dtUserSPlistONE.add(dtUserSPlistONEAll.get(col));
                    loop2 = col;
                } else {
                    break;
                }
            }
            row = loop2;


            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "工作线程分批处理，此次处理的消息list.size():" + dtUserSPlistONE.size() + "]");


            CngMcdrChargeCfgData mcdrChargeCfgDT = new CngMcdrChargeCfgData();
            List<String> threadLocalUnBillLines = new ArrayList<>();
            List<CngMcdrData> threadLocalMcdrList = new ArrayList<>();
            List<CngDcdrDetailData> threadLocalMcdrDetailList = new ArrayList<>();
            List<CngDcdrDetailData> threadLocalSettleCdrList = new ArrayList<>();
            List<CngDcdrDetailData> threadLocalFailAuthlList = new ArrayList<>();


            dealSp(dtUserSPlistONE, bCharge, mcdrChargeCfgDT, dtOneUserMcdrList, dbResultInfo, threadLocalUnBillLines, threadLocalMcdrList,
                    threadLocalMcdrDetailList, threadLocalSettleCdrList, threadLocalFailAuthlList);

            Object[] outobj = new Object[9];
            outobj[0] = "LHZQ";
            outobj[1] = bCharge;
            outobj[2] = threadLocalUnBillLines;
            outobj[3] = threadLocalMcdrDetailList;
            outobj[4] = threadLocalMcdrDetailList;
            outobj[5] = threadLocalSettleCdrList;
            outobj[6] = mcdrChargeCfgDT;
            outobj[7] = dbResultInfo;
            outobj[8] = threadLocalFailAuthlList;
            message.add(outobj);

        }


    }

    private void dealSp(List<CngUserSpData> dtUserSPlistONE, boolean bCharge, CngMcdrChargeCfgData mcdrChargeCfgDT,
                        List<String> dtOneUserMcdrList, DbResultInfo dbResultInfo,
                        List<String> threadLocalUnBillLines, List<CngMcdrData> threadLocalMcdrList,
                        List<CngDcdrDetailData> threadLocalMcdrDetailList, List<CngDcdrDetailData> threadLocalSettleCdrList,
                        List<CngDcdrDetailData> threadLocalFailAuthlList) {


        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---dealSp]");


        List<String> m_fOrdTimeList = new ArrayList<>();
        CngUserSpData t_orderInfo = new CngUserSpData();
        CngExdSpCodeDefAllData threadLocalspInfo = new CngExdSpCodeDefAllData();
        CngExdSpOperDefAllData threadLocaloperInfo = new CngExdSpOperDefAllData();
        ParamBean paramBean = new ParamBean();
        paramBean.setErrorCode("0");
        Map<String, String> threadLocalMapList = new HashMap<>();

        //处理该业务
        processMcdr(dtUserSPlistONE, bCharge, mcdrChargeCfgDT, m_fOrdTimeList, t_orderInfo, dtOneUserMcdrList, dbResultInfo,
                threadLocalspInfo, threadLocaloperInfo, paramBean, threadLocalMapList, threadLocalUnBillLines,
                threadLocalMcdrList, threadLocalMcdrDetailList, threadLocalSettleCdrList, threadLocalFailAuthlList);


    }

    private void processMcdr(List<CngUserSpData> dtUserSPlistONE, boolean bCharge, CngMcdrChargeCfgData mcdrChargeCfgDT, List<String> m_fOrdTimeList, CngUserSpData t_orderInfo,
                             List<String> dtOneUserMcdrList, DbResultInfo dbResultInfo, CngExdSpCodeDefAllData threadLocalspInfo,
                             CngExdSpOperDefAllData threadLocaloperInfo, ParamBean paramBean, Map<String, String> threadLocalMapList, List<String> threadLocalUnBillLines,
                             List<CngMcdrData> threadLocalMcdrList, List<CngDcdrDetailData> threadLocalMcdrDetailList,
                             List<CngDcdrDetailData> threadLocalSettleCdrList,
                             List<CngDcdrDetailData> threadLocalFailAuthlList) {
        if (!isNeedProcess(dtUserSPlistONE, mcdrChargeCfgDT, dbResultInfo, threadLocalspInfo, threadLocaloperInfo)) {
            return;
        }

        if (!isSpNeedProcess(dtUserSPlistONE, bCharge, mcdrChargeCfgDT, m_fOrdTimeList, t_orderInfo)) {
            return;
        }

        processErrorCode(t_orderInfo, mcdrChargeCfgDT, dbResultInfo, paramBean, threadLocaloperInfo, threadLocalMapList);
        if (Montnetconfig.bMonthSwitch.get()) {
            startFormatChangeMonthCdr(bCharge, t_orderInfo, mcdrChargeCfgDT, dtOneUserMcdrList, paramBean,
                    dbResultInfo, threadLocaloperInfo, threadLocalspInfo, threadLocalMapList, threadLocalUnBillLines,
                    threadLocalMcdrList, threadLocalMcdrDetailList, threadLocalSettleCdrList);
        } else {

            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "m_nErrCode:" + paramBean.getErrorCode() + "]");

            String nextValidTime = "";
            //包月计费 还需要进行余额鉴权
            if (t_orderInfo.getCycleTimes() > 1 && dbResultInfo.getMapAccBaseCfg().containsKey(t_orderInfo.getsSpID())) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "AAAAAAA"+t_orderInfo.getCycleTimes());

                StringBuffer buffer = new StringBuffer();
                //流水号 省DOMAIN+3位省代码+14位组包时间YYYYMMDDHH24MMSS+流水号，序号增量步长为1。
                //省DOMAIN=BOSS，省代码=250
                buffer.append(Montnetconfig.getOperationSrl()).append(COMMA_STR)
                        //计费号码
                        .append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR)
                        //创建时间
                        .append(DateUtils.getCurrDate("yyyyMMddHHmmss")).append(COMMA_STR)
                        //业务类型代码
                        .append(dbResultInfo.getMapAccBaseCfg().get(t_orderInfo.getsSpID())).append(COMMA_STR)
                        //企业代码
                        .append(t_orderInfo.getsSpID()).append(COMMA_STR)
                        //业务编码
                        .append(t_orderInfo.getsSpBizID()).append(COMMA_STR)
                        //计费类型，灵活周期计费业务取值为7-包天计费
                        .append(7).append(COMMA_STR)
                        //订单编号，同业务订购时的订单编号
                        .append(t_orderInfo.getOrderId()).append(COMMA_STR);


                //用户状态不正常
                if ("100".equals(paramBean.getErrorCode())) {
                    paramBean.setErrorCode("700");
                    //扣费失败
                    buffer.append(1).append(COMMA_STR);
                    //扣费说明  <chargeResultDesc>0000</chargeResultDesc>  扣费成功  <chargeResultDesc>3002</chargeResultDesc> 扣费失败
                    buffer.append("3002").append(COMMA_STR);
                } else {
                    if (dbResultInfo.getAcctbkBalance().get(t_orderInfo.getsUserID()) == null) {
                        //获取余额
                        paramBean.setErrorCode(balanceAuth(t_orderInfo));
                    }

                    if (Montnetconfig.acctbkBalance.get().get(t_orderInfo.getsUserID()) < threadLocaloperInfo.getnFEE()) {
                        //如果用户余额不足，则置余额鉴权错误码
                        paramBean.setErrorCode("700");
                        //扣费失败
                        buffer.append(1).append(COMMA_STR);
                        //扣费说明  <chargeResultDesc>0000</chargeResultDesc>  扣费成功  <chargeResultDesc>3002</chargeResultDesc> 扣费失败
                        buffer.append("3002").append(COMMA_STR);
                    } else {

                        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "  扣费之前,userId : " + t_orderInfo.getsUserID() + "  fee : " + Montnetconfig.acctbkBalance.get().get(t_orderInfo.getsUserID()));


                        //后付费用户不用判断余额是否充足。
                        if (Montnetconfig.acctbkBalance.get().get(t_orderInfo.getsUserID()) != 9999999L) {
                            Montnetconfig.acctbkBalance.get().put(t_orderInfo.getsUserID(), Montnetconfig.acctbkBalance.get().get(t_orderInfo.getsUserID()) -
                                    threadLocaloperInfo.getnFEE());
                        }


                        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "  扣费之后,userId : " + t_orderInfo.getsUserID() + "  fee : " + Montnetconfig.acctbkBalance.get().get(t_orderInfo.getsUserID()));

                        //扣费成功
                        buffer.append(0).append(COMMA_STR);
                        //扣费说明  <chargeResultDesc>0000</chargeResultDesc>  扣费成功  <chargeResultDesc>3002</chargeResultDesc> 扣费失败
                        buffer.append("0000").append(COMMA_STR);
                    }

                }

                //重新设置小周期生效时间
                // 如果用户4月5号9：00订购半年包，则生效时间为4月5号9：00，到期时间为10月5日23:59:59，第1个小周期失效日期为5月5日23:59:59，从5月起后续每月扣费日为5日，后续小周期生/失效时间为每月6日00:00:00—下月5日23:59:59。
                LocalDateTime orderDate = LocalDateTime.parse(t_orderInfo.getsStartDate(), Montnetconfig.dateTimeFormatter);
                
                if (t_orderInfo.getCycleTimes() > 1) {
                    LocalDateTime orderStartTime = orderDate.plusMonths(t_orderInfo.getCycleTimes() - 1).plusDays(1L).withHour(0).withMinute(0).withSecond(0);
                    nextValidTime = orderStartTime.format(Montnetconfig.dateTimeFormatter);
                    //此行代码注释 挪至尾行
                    //t_orderInfo.setsStartDate(orderStartTime.format(Montnetconfig.dateTimeFormatter));
                }
                //小周期序号，整数，第几次扣费
                buffer.append(t_orderInfo.getCycleTimes()).append(COMMA_STR)
                        //扣费时间
                        .append(DateUtils.getCurrDate("yyyyMMddHHmmss")).append(COMMA_STR)
                        //小周期生效时间
                        .append(nextValidTime).append(COMMA_STR)
                        //地市编码
                        .append(Montnetconfig.getCityID()).append(COMMA_STR)
                        //spname
                        .append(threadLocalspInfo.getStrSpName()).append(COMMA_STR)
                        //opname
                        .append(threadLocaloperInfo.getStrOPERNAME()).append(COMMA_STR)
                        //userId
                        .append(t_orderInfo.getsUserID()).append(COMMA_STR)
                        //fee
                        .append(threadLocaloperInfo.getnFEE());
                //第二次小周期才能发送rocketmq
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "AAAAAAA"+t_orderInfo.getCycleTimes());
                if (t_orderInfo.getCycleTimes() > 1) {
                    MqUtil.sendMqMsg(buffer.toString());
                }
            }

            //如果扣费失败， 入流水表排重
            if ("700".equals(paramBean.getErrorCode())) {
                formateFailAuth(t_orderInfo, dbResultInfo, paramBean, threadLocaloperInfo, threadLocalMapList, threadLocalFailAuthlList);
            } else if ("0".equals(paramBean.getErrorCode())) {
                //初始化话单
                formatCdr(t_orderInfo, mcdrChargeCfgDT, dbResultInfo, threadLocaloperInfo, threadLocalspInfo, threadLocalMapList, threadLocalUnBillLines);


                //初始化已经收过费的文件
                formatMBill(t_orderInfo, dtOneUserMcdrList, dbResultInfo, threadLocaloperInfo, threadLocalspInfo,
                        threadLocalMapList, threadLocalUnBillLines, threadLocalMcdrList, paramBean, threadLocalMcdrDetailList);

                dbResultInfo.m_mcdrNumOneDay++;
            }
            if (t_orderInfo.getCycleTimes() > 1) {
                //此行代码注释
                t_orderInfo.setsStartDate(nextValidTime);
            }
        }

    }

    public boolean isSpNeedProcess(List<CngUserSpData> dtUserSPlistONE, boolean bCharge, CngMcdrChargeCfgData mcdrChargeCfgDT,
                                   List<String> m_fOrdTimeList, CngUserSpData t_orderInfo) {
        if (!isChargeModeEqual(mcdrChargeCfgDT.getnCHARGEMODE(), dtUserSPlistONE, m_fOrdTimeList, t_orderInfo)) {

            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "charge condition not valid]");

            return false;
        }

        return true;
    }

    public boolean isChargeModeEqual(long nNewChargeMode, List<CngUserSpData> dtOneUserSPlist, List<String> m_fOrdTimeList, CngUserSpData t_orderInfo) {
        boolean bRet = false;
        switch (Integer.parseInt(String.valueOf(nNewChargeMode))) {
            case 5:
            case 6:
                bRet = isChargeMode5(dtOneUserSPlist, m_fOrdTimeList, t_orderInfo);
                break;
            /*case 6:
                bRet = isChargeMode6(dtOneUserSPlist, m_fOrdTimeList, t_orderInfo);
                break;*/
            default:
                bRet = false;
                break;
        }

        return bRet;
    }

    public boolean isChargeMode5(List<CngUserSpData> dtOneUserSPlist, List<String> mfOrdTimeList, CngUserSpData torderInfo) {
        long tlab;
        boolean tbFlag;
        long tiBeginDate, tiEndDate;

        for (tlab = 0, tbFlag = false;
             (tlab < dtOneUserSPlist.size() && !tbFlag);
             tlab++) {
            torderInfo.setnBillFlag(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getnBillFlag());
            torderInfo.setsDoMainID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsDoMainID());
            torderInfo.setsEndDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsEndDate());
            torderInfo.setsPriorOrderDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsPriorOrderDate());
            torderInfo.setsProdInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsProdInstanceID());
            torderInfo.setsSpBizID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpBizID());
            torderInfo.setsSpID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpID());
            torderInfo.setsSpType(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpType());
            torderInfo.setsStartDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsStartDate());
            torderInfo.setsUserID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserID());
            torderInfo.setInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getInstanceID());
            torderInfo.setsUserIDDone(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserIDDone());
            torderInfo.setOrderId(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getOrderId());

            torderInfo.setCycleTimes(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getCycleTimes());

            tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
            tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));

            doSpecialCharge(torderInfo, tiBeginDate, tiEndDate);

            if (Long.parseLong(torderInfo.getsStartDate()) >= Long.parseLong(torderInfo.getsEndDate())) {

                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "失效时间在前，则跳过" + "1111111111111]");

                continue;
            } else if ((tiBeginDate < Montnetconfig.changeMonth.get() * 100 + 1) &&
                    (tiEndDate >= Montnetconfig.changeMonth.get() * 100 + 1)) {
                tbFlag = true;
            } else if ((tiBeginDate / 100) == Montnetconfig.changeMonth.get()) {
                tbFlag = true;
            }
        }
        return tbFlag;
    }

    public void doSpecialCharge(CngUserSpData torderInfo, long tiBeginDate, long tiEndDate) {

        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_orderInfo.getsStartDate():" + torderInfo.getsStartDate() + "]");
        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_orderInfo.getsEndDate():" + torderInfo.getsEndDate() + "]");
        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_iBeginDate:" + tiBeginDate + "]");
        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "Montnetconfig.getChangeMonth()*100+1:" + (Montnetconfig.changeMonth.get() * 100 + 1) + "]");
        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_iEndDate:" + tiEndDate + "]");


        if ("910477".equals(torderInfo.getsSpID())) {
            DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
            try {
                Date date = df.parse(torderInfo.getsEndDate());
                date.setTime(date.getTime() + 3 * 60 * 60 * 1000);
                torderInfo.setsEndDate(df.format(date));
            } catch (ParseException e) {
                LogUtil.error(LogProperty.LOGTYPE_DETAIL + " 时间格式转换异常 ！", e);
            }
        }
    }


    public boolean isChargeMode6(List<CngUserSpData> dtOneUserSPlist, List<String> mfOrdTimeList, CngUserSpData torderInfo) {
        mfOrdTimeList.clear();
        long tlab;
        boolean tbFlag;
        long tiBeginDate, tiBeginTime, tiEndDate, tiEndTime;
        long tiOrderTimes;
        long tnowDay, tnowTime;

        Date date = new Date();
        DateFormat formatrdealTime = new SimpleDateFormat("yyyyMMddHHmmss");
        String rdealTime = formatrdealTime.format(date);

        tnowDay = Long.parseLong(rdealTime.substring(0, 8));
        tnowTime = Long.parseLong(rdealTime.substring(8, rdealTime.length()));

        for (tlab = 0, tbFlag = false, tiOrderTimes = 0;
             (tlab < dtOneUserSPlist.size() && !tbFlag);
             tlab++) {
            torderInfo.setnBillFlag(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getnBillFlag());
            torderInfo.setsDoMainID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsDoMainID());
            torderInfo.setsEndDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsEndDate());
            torderInfo.setsPriorOrderDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsPriorOrderDate());
            torderInfo.setsProdInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsProdInstanceID());
            torderInfo.setsSpBizID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpBizID());
            torderInfo.setsSpID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpID());
            torderInfo.setsSpType(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpType());
            torderInfo.setsStartDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsStartDate());
            torderInfo.setsUserID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserID());
            torderInfo.setInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getInstanceID());
            torderInfo.setsUserIDDone(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserIDDone());
            torderInfo.setOrderId(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getOrderId());

            torderInfo.setCycleTimes(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getCycleTimes());

            tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
            tiBeginTime = Long.parseLong(torderInfo.getsStartDate().substring(8, 14));
            tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));
            tiEndTime = Long.parseLong(torderInfo.getsEndDate().substring(8, 14));


            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_orderInfo.getsStartDate():" + torderInfo.getsStartDate() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_nowDay:" + tnowDay + "][t_nowTime:" + tnowTime + "][t_iBeginDate:" + tiBeginDate + "][t_iBeginTime:" + tiBeginTime
                    + "][t_iEndDate:" + tiEndDate + "][t_iEndTime:" + tiEndTime + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "11:" + addDates(tiBeginDate, 4) + "][22:" + addDates(20100228, 4) + "][33:" + addDates(20101231, 4) + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_orderInfo.getsStartDate():" + torderInfo.getsStartDate() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_orderInfo.getsEndDate():" + torderInfo.getsEndDate() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_iBeginDate:" + tiBeginDate + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "Montnetconfig.getChangeMonth()*100+1:" + Montnetconfig.changeMonth.get() * 100 + 1 + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_iEndDate:" + tiEndDate);
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "Montnetconfig.getChangeMonth()*100+1:" + Montnetconfig.changeMonth.get() * 100 + 1 + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_iBeginDate/100:" + tiBeginDate / 100 + "]");


            if ((tiBeginDate / 100) == Montnetconfig.changeMonth.get()) {
                if (!repeatOrderTime(torderInfo.getsPriorOrderDate(), mfOrdTimeList)) {
                    tiOrderTimes++;
                    mfOrdTimeList.add(torderInfo.getsPriorOrderDate());
                }
                if (isCharge61(tiOrderTimes, torderInfo)) {
                    return true;
                }
            } else {
                if (isCharge62(torderInfo)) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isCharge62(CngUserSpData torderInfo) {
        long tiBeginDate, tiBeginTime, tiEndDate, tiEndTime;

        tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));

        tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));


        long tnowDay, tnowTime;

        Date date = new Date();
        DateFormat formatrdealTime = new SimpleDateFormat("yyyyMMddHHmmss");
        String rdealTime = formatrdealTime.format(date);

        tnowDay = Long.parseLong(rdealTime.substring(0, 8));


        if (Long.parseLong(torderInfo.getsStartDate()) >= Long.parseLong(torderInfo.getsEndDate())) {
            return false;
        } else if ((tiBeginDate < Montnetconfig.changeMonth.get() * 100 + 1) &&
                (tiEndDate >= Montnetconfig.changeMonth.get() * 100 + 1)) {

            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_iEndDate:" + tiEndDate + "AddDates(t_iBeginDate,4):" + addDates(tiBeginDate, 4) + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_nowDay:" + tnowDay + "AddDates(t_iBeginDate,4)):" + addDates(tiBeginDate, 4) + "]");

            if (isCharge6(torderInfo)) {
                return true;
            }
        }
        return false;
    }

    public boolean isCharge6(CngUserSpData torderInfo) {
        long tiBeginDate, tiBeginTime, tiEndDate, tiEndTime;
        tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
        tiBeginTime = Long.parseLong(torderInfo.getsStartDate().substring(8, 14));
        tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));
        tiEndTime = Long.parseLong(torderInfo.getsEndDate().substring(8, 14));

        long tnowDay, tnowTime;

        Date date = new Date();
        DateFormat formatrdealTime = new SimpleDateFormat("yyyyMMddHHmmss");
        String rdealTime = formatrdealTime.format(date);

        tnowDay = Long.parseLong(rdealTime.substring(0, 8));
        tnowTime = Long.parseLong(rdealTime.substring(8, rdealTime.length()));

        if ((tiEndDate > addDates(tiBeginDate, 3) || (tiEndDate == addDates(tiBeginDate, 3) && tiEndTime > tiBeginTime)) && (tnowDay > addDates(tiBeginDate, 3) || (tnowDay == addDates(tiBeginDate, 3) && tnowTime > tiBeginTime))) {
            return true;
        }
        return false;
    }

    public boolean isCharge61(long tiOrderTimes, CngUserSpData torderInfo) {
        long tiBeginDate, tiBeginTime, tiEndDate, tiEndTime;
        tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
        tiBeginTime = Long.parseLong(torderInfo.getsStartDate().substring(8, 14));
        tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));
        tiEndTime = Long.parseLong(torderInfo.getsEndDate().substring(8, 14));

        long tnowDay, tnowTime;

        Date date = new Date();
        DateFormat formatrdealTime = new SimpleDateFormat("yyyyMMddHHmmss");
        String rdealTime = formatrdealTime.format(date);

        tnowDay = Long.parseLong(rdealTime.substring(0, 8));
        tnowTime = Long.parseLong(rdealTime.substring(8, rdealTime.length()));

        if (tiOrderTimes > 1) {
            return true;
        } else {
            if (isTimeNeedCharge(tiEndDate, tiBeginDate, tiEndTime, tiBeginTime, tnowDay, tnowTime)) {
                return true;
            }
        }
        return false;
    }

    public boolean isTimeNeedCharge(long tiEndDate, long tiBeginDate, long tiEndTime, long tiBeginTime, long tnowDay, long tnowTime) {
        if ((tiEndDate > addDates(tiBeginDate, 3) || (tiEndDate == addDates(tiBeginDate, 3) && tiEndTime > tiBeginTime)) && (tnowDay > addDates(tiBeginDate, 3) || (tnowDay == addDates(tiBeginDate, 3) && tnowTime > tiBeginTime))) {
            return true;
        }
        return false;
    }

    public boolean repeatOrderTime(String r_orderTime, List<String> m_fOrdTimeList) {
        for (int row = 0; row < m_fOrdTimeList.size(); row++) {
            if (r_orderTime.equals(m_fOrdTimeList.get(row))) {
                return true;
            }
        }
        return false;
    }

    public long addDates(long nYearDate, int nModNum) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        Date tmpStartTime = null;
        try {
            tmpStartTime = format.parse(String.valueOf(nYearDate));
        } catch (ParseException e) {
            LogUtil.error(TAG + "时间转换异常", e);
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(tmpStartTime);
        cal.add(Calendar.DATE, nModNum);
        long beginDate = Long.parseLong(new SimpleDateFormat("yyyyMMdd").format(cal.getTime()));
        return beginDate;
    }


    public boolean isNeedProcess(List<CngUserSpData> dtUserSPlistONE, CngMcdrChargeCfgData
            mcdrChargeCfgDT, DbResultInfo dbResultInfo,
                                 CngExdSpCodeDefAllData threadLocalspInfo, CngExdSpOperDefAllData threadLocaloperInfo) {

        if (dtUserSPlistONE.size() == 0) {
            return false;
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---ProcessMcdr" + dtUserSPlistONE.size() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "****user_id:" + dtUserSPlistONE.get(0).getsUserID() + " ][sp_id:" + dtUserSPlistONE.get(0).getsSpID() + "][biz_id:" + dtUserSPlistONE.get(0).getsSpBizID() + "]");
        }

        //取SP信息|取SP业务定义信息|取业务配置信息
        if (!getSpCodeCfgBySpCode(dtUserSPlistONE, dbResultInfo, threadLocalspInfo) || !getSpOperbyOperCode(dtUserSPlistONE, dbResultInfo, threadLocaloperInfo) ||
                !getSpChargeCfg(dtUserSPlistONE, mcdrChargeCfgDT, dbResultInfo, threadLocaloperInfo)) {
            return false;
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "mcdrChargeCfgDT.getnCHARGEMODE():" + mcdrChargeCfgDT.getnCHARGEMODE() + "]");
        }
        return true;

    }


    public boolean getSpChargeCfg(List<CngUserSpData> dtOneUserSPlist, CngMcdrChargeCfgData mcdrChargeCfgDT, DbResultInfo dbResultInfo, CngExdSpOperDefAllData threadLocaloperInfo) {
        printSpTypeInfo(dtOneUserSPlist, threadLocaloperInfo);
        int y = 0;

        List<String> vSpType = new ArrayList<String>();

        for (int row = 0; row < dbResultInfo.getDtMcdrChargeCfgList().size(); row++) {
            printMcdrInfo(row, threadLocaloperInfo, dbResultInfo);
            if (threadLocaloperInfo.getStrSERVTYPE().equals(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSERVTYPE())) {
                vSpType.clear();
                String[] strTmp = dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSPTYPE().split("\\,", -1);

                for (int col = 0; col < strTmp.length; col++) {
                    vSpType.add(strTmp[col]);
                }

                for (y = 0; y < vSpType.size(); y++) {
                    if (dtOneUserSPlist.get(0).getsSpType().equals(vSpType.get(y))) {
                        printVspTypeInfo(vSpType, y, dtOneUserSPlist);
                        mcdrChargeCfgDT.setnBusinessType(dbResultInfo.getDtMcdrChargeCfgList().get(row).getnBusinessType());
                        mcdrChargeCfgDT.setnCHARGEMODE(dbResultInfo.getDtMcdrChargeCfgList().get(row).getnCHARGEMODE());
                        mcdrChargeCfgDT.setnFILEMAXNUM(dbResultInfo.getDtMcdrChargeCfgList().get(row).getnFILEMAXNUM());
                        mcdrChargeCfgDT.setnLineNum(dbResultInfo.getDtMcdrChargeCfgList().get(row).getnLineNum());
                        mcdrChargeCfgDT.setsCREATEDATE(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsCREATEDATE());
                        mcdrChargeCfgDT.setsDOMAINID(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsDOMAINID());
                        mcdrChargeCfgDT.setsFileName(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsFileName());
                        mcdrChargeCfgDT.setsFILENAMEPRIX(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsFILENAMEPRIX());
                        mcdrChargeCfgDT.setsSERVNAME(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSERVNAME());
                        mcdrChargeCfgDT.setsSERVTYPE(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSERVTYPE());
                        mcdrChargeCfgDT.setsSingularStatus(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSingularStatus());
                        mcdrChargeCfgDT.setsSPTYPE(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSPTYPE());


                        return true;
                    }
                }
            }
        }


        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "该业务没有找到收费配置sp_type:" + dtOneUserSPlist.get(0).getsSpType() + "][serv_type:" +
                threadLocaloperInfo.getStrSERVTYPE() +
                " ][domain_id:" + dtOneUserSPlist.get(0).getsDoMainID() + "]");

        return false;
    }

    public void printMcdrInfo(int row, CngExdSpOperDefAllData threadLocaloperInfo, DbResultInfo dbResultInfo) {

        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----operInfo.getStrSERVTYPE():" + threadLocaloperInfo.getStrSERVTYPE() + "]");
        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----cngMcdrChargeCfgDT.dtMcdrChargeCfgList.get(row).getsSERVTYPE():" +
                dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSERVTYPE() + "]");

    }

    public void printVspTypeInfo(List<String> vSpType, int y, List<CngUserSpData> dtOneUserSPlist) {

        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----dtOneUserSPlist.get(0).getsSpType():" + dtOneUserSPlist.get(0).getsSpType() + "]");
        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----vSpType.get(y):" + vSpType.get(y) + "]");

    }

    public void printSpTypeInfo(List<CngUserSpData> dtOneUserSPlist, CngExdSpOperDefAllData threadLocaloperInfo) {

        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---getSpChargeCfg]");
        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "operInfo.getStrSERVTYPE():" + threadLocaloperInfo.getStrSERVTYPE() + "]");
        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtOneUserSPlist.get(0).getsSpType():" + dtOneUserSPlist.get(0).getsSpType() + "]");

    }

    public boolean getSpOperbyOperCode(List<CngUserSpData> dtOneUserSPlist, DbResultInfo dbResultInfo, CngExdSpOperDefAllData threadLocaloperInfo) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---getSpOperbyOperCode]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "SP_ID:" + dtOneUserSPlist.get(0).getsSpID() + "][sp_biz_id:" + dtOneUserSPlist.get(0).getsSpBizID() + "]");
        }

        StringBuffer sIndex = new StringBuffer();
        sIndex.append(dtOneUserSPlist.get(0).getsSpID());
        sIndex.append(dtOneUserSPlist.get(0).getsSpBizID());

        if (dbResultInfo.getMapSpOperInfo().get(sIndex.toString()) == null) {

            LogUtil.debug("[没有找到SP业务配置信息sp_code+oper_code:" + sIndex.toString() + "]");

            return false;
        }

        for (int row = 0; row < dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).size(); row++) {
            threadLocaloperInfo.setStrSERVTYPE(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrSERVTYPE());
            threadLocaloperInfo.setStrSPCODE(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrSPCODE());
            threadLocaloperInfo.setStrOPERCODE(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrOPERCODE());
            threadLocaloperInfo.setStrOPERNAME(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrOPERNAME());
            threadLocaloperInfo.setStrBILLFLAG(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrBILLFLAG());
            threadLocaloperInfo.setnFEE(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getnFEE());

            threadLocaloperInfo.setStrVALIDDATE(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrVALIDDATE());
            threadLocaloperInfo.setStrEXPIREDATE(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrEXPIREDATE());
            threadLocaloperInfo.setnINPROP(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getnINPROP());
            threadLocaloperInfo.setnOUTPROP(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getnOUTPROP());
            threadLocaloperInfo.setnCOUNT(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getnCOUNT());
            threadLocaloperInfo.setStrSPATTR(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrSPATTR());
            threadLocaloperInfo.setBydayMode(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getBydayMode());

            resetFeeByMemberAttr(dtOneUserSPlist.get(0).getsUserID(), sIndex.toString(), threadLocaloperInfo, dbResultInfo);

            //如果是第三方支付的话，fee=0
            if ("2".equals(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getPkgType())) {
                threadLocaloperInfo.setnFEE(0);
            }

            return true;
        }

        LogUtil.debug("DTL" + "[没有找到SP业务配置信息sp_code+oper_code:" + sIndex + "]");


        return false;
    }

    public void resetFeeByMemberAttr(long userId, String sIndex, CngExdSpOperDefAllData threadLocaloperInfo, DbResultInfo dbResultInfo) {
        if (!"698039".equals(threadLocaloperInfo.getStrSPCODE())) {
            return;
        }

        int memberLevel = 0;

        memberLevel = getUserServiceInfoByUserId(userId);

        //sp_code=698039, oper_code = 698039018080000003特殊处理，打7折
        if (memberLevel == 4) {
            resetFeeByMemberMusic(sIndex, dbResultInfo, threadLocaloperInfo);
            return;
        }

        if (!dbResultInfo.getMapSpMusicAttr().containsKey(sIndex)) {
            return;
        }

        for (int row = 0; row < dbResultInfo.getMapSpMusicAttr().get(sIndex).size(); row++) {
            if (memberLevel == dbResultInfo.getMapSpMusicAttr().get(sIndex).get(row).getMemberAttr()) {
                threadLocaloperInfo.setnFEE(dbResultInfo.getMapSpMusicAttr().get(sIndex).get(row).getnFEE());
                threadLocaloperInfo.setServiceClass(dbResultInfo.getMapSpMusicAttr().get(sIndex).get(row).getServiceClass());
                threadLocaloperInfo.setVcrbtType(dbResultInfo.getMapSpMusicAttr().get(sIndex).get(row).getVcrbtType());
                break;
            }
        }
    }

    public void resetFeeByMemberMusic(String sIndex, DbResultInfo dbResultInfo, CngExdSpOperDefAllData threadLocaloperInfo) {
        if (dbResultInfo.getMapSpMemberAttr().containsKey(sIndex)) {
            for (int row = 0; row < dbResultInfo.getMapSpMemberAttr().get(sIndex).size(); row++) {
                if (3 == dbResultInfo.getMapSpMemberAttr().get(sIndex).get(row).getMemberAttr()) {
                    threadLocaloperInfo.setnFEE(dbResultInfo.getMapSpMemberAttr().get(sIndex).get(row).getnFEE());
                    return;
                }
            }
        }

        if (dbResultInfo.getMapSpMemberAttr().containsKey(sIndex)) {
            for (int row = 0; row < dbResultInfo.getMapSpMemberAttr().get(sIndex).size(); row++) {
                if (3 == dbResultInfo.getMapSpMemberAttr().get(sIndex).get(row).getMemberAttr()) {
                    threadLocaloperInfo.setnFEE(dbResultInfo.getMapSpMemberAttr().get(sIndex).get(row).getnFEE());
                    return;
                }
            }
        }
    }


    public int getUserServiceInfoByUserId(long userId) {
        int memberLevel = 0;

        try {
            List<UserServiceInfo> returnList = iUserInfoManage.getUserServiceInfoByUserId(CacheNameDef.CACHE_USER_SERVICE, userId);
            if (returnList != null) {
                memberLevel = getMemberLevel(returnList);
            }

        } catch (Exception e) {
            LogUtil.error("[" + TAG + "根据key:" + userId + "查询" + CacheNameDef.CACHE_USER_SERVICE + "表异常，查询数据失败！]", e);
            return -1;
        }
        return memberLevel;
    }

    public int getMemberLevel(List<UserServiceInfo> returnList) {
        Date date = new Date();
        DateFormat formatDate = new SimpleDateFormat("yyyyMMddHHmmss");
        long nowDate = Long.parseLong(formatDate.format(date));

        int memberLevel = 0;

        for (int row = 0; row < returnList.size(); row++) {
            UserServiceInfo userServiceInfo = returnList.get(row);
            if (userServiceInfo.getStartDate() <= nowDate && userServiceInfo.getEndDate() >= nowDate) {
                //高级会员服务编号
                if (userServiceInfo.getServiceCode() == 120000025) {
                    memberLevel = 2;
                    break;
                }
                //普通会员服务编号
                else if (userServiceInfo.getServiceCode() == 120000026) {
                    memberLevel = 1;
                    break;
                }
                //特级会员服务代码
                else if (userServiceInfo.getServiceCode() == 120000262) {
                    memberLevel = 3;
                    break;
                } else if (userServiceInfo.getServiceCode() == 2400000261L) {
                    memberLevel = 4;
                    break;
                }
            }
        }
        return memberLevel;
    }

    public boolean getSpCodeCfgBySpCode(List<CngUserSpData> dtOneUserSPlist, DbResultInfo dbResultInfo,
                                        CngExdSpCodeDefAllData threadLocalspInfo) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtOneUserSPlist.get(0).getsSpID() :" + dtOneUserSPlist.get(0).getsSpID() + "]");
        }

        if (dbResultInfo.mapSpInfo.containsKey(dtOneUserSPlist.get(0).getsSpID())) {
            List<CngExdSpCodeDefAllData> listData = dbResultInfo.mapSpInfo.get(dtOneUserSPlist.get(0).getsSpID());

            threadLocalspInfo.setStrServType(listData.get(0).getStrServType());
            threadLocalspInfo.setStrSpCode(listData.get(0).getStrSpCode());
            threadLocalspInfo.setStrSpName(listData.get(0).getStrSpName());
            threadLocalspInfo.setStrSpType(listData.get(0).getStrSpType());
            threadLocalspInfo.setStrServCode(listData.get(0).getStrServCode());
            threadLocalspInfo.setnProvCode(listData.get(0).getnProvCode());
            threadLocalspInfo.setnBalProv(listData.get(0).getnBalProv());
            threadLocalspInfo.setStrDevCode(listData.get(0).getStrDevCode());
            threadLocalspInfo.setStrValidDate(listData.get(0).getStrValidDate());
            threadLocalspInfo.setStrExpireDate(listData.get(0).getStrExpireDate());
            threadLocalspInfo.setStrDescRiption(listData.get(0).getStrDescRiption());
            threadLocalspInfo.setStrSpAttr(listData.get(0).getStrSpAttr());
            return true;
        }

        if (LogUtil.isWarnEnable()) {
            LogUtil.debug("DTL" + TAG + "[没有找到SP代码配置信息sp_code:" + dtOneUserSPlist.get(0).getsSpID() + "]");
        }
        return false;
    }


    private void formateFailAuth(CngUserSpData dtOneUserSPlist, DbResultInfo dbResultInfo, ParamBean paramBean,
                                 CngExdSpOperDefAllData threadLocaloperInfo, Map<String, String> threadLocalMapList,
                                 List<CngDcdrDetailData> threadLocalFailAuthlList) {
        CngDcdrDetailData mcdrDetailDT = new CngDcdrDetailData();

        if (dtOneUserSPlist.getnBillFlag() == 0 || dtOneUserSPlist.getnBillFlag() == 1 || dtOneUserSPlist.getnBillFlag() == 4) {
            mcdrDetailDT.setRecType(1);
        } else {
            //REC_TYPE_UNCHARGE					= 2;        //不收费明细SP
            mcdrDetailDT.setRecType(2);
        }

        Date date = new Date();
        DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        String nowtime = format.format(date);

        mcdrDetailDT.setUserId(dtOneUserSPlist.getsUserID());
        mcdrDetailDT.setBillMonth(Montnetconfig.changeMonth.get());

        mcdrDetailDT.setBusiType(dtOneUserSPlist.getsSpType());
        mcdrDetailDT.setSpCode(dtOneUserSPlist.getsSpID());
        mcdrDetailDT.setOperCode(dtOneUserSPlist.getsSpBizID());
        mcdrDetailDT.setChargeFee(threadLocaloperInfo.getnFEE());
        mcdrDetailDT.setDomainId(dtOneUserSPlist.getsDoMainID());
        mcdrDetailDT.setServiceNumber(threadLocalMapList.get("SERVICE_NUMBER"));
        mcdrDetailDT.setOrderStartDate(dtOneUserSPlist.getsStartDate());
        mcdrDetailDT.setOrderEndDate(dtOneUserSPlist.getsEndDate());
        if (null != threadLocalMapList.get("STATUS")) {
            mcdrDetailDT.setUserStatus(Integer.parseInt(threadLocalMapList.get("STATUS")));
        }

        mcdrDetailDT.setErrCode(Integer.parseInt(paramBean.getErrorCode()));
        mcdrDetailDT.setCreateDate(nowtime);
        mcdrDetailDT.setChargeType((int) Montnetconfig.getChargeType());
        mcdrDetailDT.setOrderId(dtOneUserSPlist.getOrderId());
        mcdrDetailDT.setCityId(Montnetconfig.getCityID());
        if (threadLocaloperInfo.getBydayMode() != null && !"".equals(threadLocaloperInfo.getBydayMode().trim())) {
            mcdrDetailDT.setBydayMode(Integer.valueOf(threadLocaloperInfo.getBydayMode()));
        }
        mcdrDetailDT.setCycleTimes(dtOneUserSPlist.getCycleTimes());

        threadLocalFailAuthlList.add(mcdrDetailDT);


        LogUtil.debug(LogProperty.LOGTYPE_CALL, "鉴权失败的动态月流水记录：" + threadLocalFailAuthlList);


    }

    private String balanceAuth(CngUserSpData t_orderInfo) {
        try {
            if (null != t_orderInfo.getsSpID()) {
                Long currentTime = Long.valueOf(DateTool.getCurrentDate("yyyyMMddHHmmss"));
                //根据userId求取账务关系
                AcctAccountRelationInfo acctAccountRelation = getAcctAccountRelationByUserId(t_orderInfo.getsUserID(), currentTime);

                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "求取到的账务关系：acctAccountRelation =" + acctAccountRelation + "]");


                long accountId = acctAccountRelation.getAccountId();
                long userID = t_orderInfo.getsUserID();
                //根据userId获取用户资料
                List<AcctAccountInfo> infos = getAcctAccountInfoByAccountId(accountId);
                AcctAccountInfo acctAccountInfo = infos.get(0);

                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "求取到的用户资料：acctAccountInfo =" + acctAccountInfo + "]");

                short payMode = acctAccountInfo.getPayMode();
                //根据accountId求取付费方式
                List<Object> acctPayModeList = getAcctPaymodeByAccountId(accountId);


                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "求取到的付费方式集合：acctPayModeList =" + acctPayModeList + "]");

                if (acctPayModeList != null && !acctPayModeList.isEmpty()) {
                    for (int k = 0; k < acctPayModeList.size(); k++) {
                        AcctPayModeInfo acctPayModeInfo = (AcctPayModeInfo) acctPayModeList.get(k);
                        if (acctPayModeInfo.getStartDate() <= currentTime
                                && (acctPayModeInfo.getEndDate() >= currentTime || acctPayModeInfo.getEndDate() == 0)) {

                            LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "重置accountId[" + accountId + "]付费方式[" + acctAccountInfo.getPayMode() + "--->"
                                    + acctPayModeInfo.getPayMode() + "]");

                            payMode = acctPayModeInfo.getPayMode();
                        }
                    }
                }

                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "求取到的付费方式：acctAccountInfo.getPayMode =" + acctAccountInfo.getPayMode() + "]");


                // 预付费用户且企业编码在字典表中。后付费用户不进行余额鉴权
                if (payMode == 1) {
                    Montnetconfig.acctbkBalance.get().put(userID, 9999999L);
                    return "0";
                }

                //根据accountId求取用户账本
                List<AcctbkInfo> acctbkInfo = getAcctbkInfo(accountId, userID, currentTime.toString());
                long acctbkFee = 0;
                for (int i = 0; i < acctbkInfo.get(0).getListOfDetail().size(); i++) {
                    AcctbkDetail acctbkDetail = acctbkInfo.get(0).getListOfDetail().get(i);
                    if (acctbkDetail.getAcctbkId() == 2 || acctbkDetail.getAcctbkId() == 5) {
                        acctbkFee += acctbkDetail.getCurrentBalance();
                    }
                }


                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "求取到的用户账本：acctbkInfo =" + acctbkInfo + "]");
                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "2和5账本的金额总计为：" + acctbkFee + "  userId : " + userID);


                Montnetconfig.acctbkBalance.get().put(userID, acctbkFee);
            }
        } catch (Exception e) {
            LogUtil.error("余额鉴权失败", e);
            return "700";
        }

        return "0";
    }

    public List<AcctbkInfo> getAcctbkInfo(long accountId, long userId, String chargeDate) {

        List<AcctbkInfo> acctbkInfoList = getAcctbkByAcctId(accountId);
        return acctbkInfoList;
    }


    public List<AcctbkInfo> getAcctbkByAcctId(long accountId) {
        List<Map<String, Object>> resultList = null;
        List<AcctbkInfo> returnAcctbk = new ArrayList<AcctbkInfo>();
        try {
            resultList = iCreateMonManage.getAcctbkByAcctId(accountId);
            if (resultList == null || (resultList != null && resultList.size() == 0)) {
                LogUtil.warn(TAG + "QUERY_BY_DB根据account_id[" + accountId + "]获取[acctbk]表记录数为空，无账本更新明细需要处理");
            }
            LogUtil.debug(TAG + "QUERY_BY_DB根据account_id[" + accountId + "]获取[acctbk]表记录数为" + resultList.size());
            returnAcctbk = this.convertAcctbkReturnList(resultList);
        } catch (Exception e) {
            LogUtil.error("根据account_id[" + accountId + "]获取[acctbk]表记录失败", e);
        }


        return returnAcctbk;
    }

    private List<AcctbkInfo> convertAcctbkReturnList(List<Map<String, Object>> resultList) {
        List<AcctbkInfo> acctbkInfoList = new ArrayList<AcctbkInfo>();
        AcctbkInfo acctbkInfo = new AcctbkInfo();
        if (resultList != null && !resultList.isEmpty()) {
            for (int i = 0; i < resultList.size(); ++i) {
                acctbkInfo.setAccountID(Long.parseLong(String.valueOf(resultList.get(i).get("ACCOUNT_ID"))));
                acctbkInfo.setInfoKey(new AcctbkInfo.AcctbkInfoKey(Long.parseLong(String.valueOf(resultList.get(i).get("ACCOUNT_ID")))));
                AcctbkDetail acctbk = new AcctbkDetail();
                acctbk.setAccountId(Long.parseLong(String.valueOf(resultList.get(i).get("ACCOUNT_ID"))));
                acctbk.setAcctbkId(Short.parseShort(String.valueOf(resultList.get(i).get("ACCTBK_ID"))));
                acctbk.setCurrentBalance(Long.parseLong(String.valueOf(resultList.get(i).get("CURRENT_BALANCE")).equals("null") ? "0" : String.valueOf(resultList.get(i).get(
                        "CURRENT_BALANCE"))));
                acctbk.setLastBalance(Long.parseLong(String.valueOf(resultList.get(i).get("LAST_BALANCE")).equals("null") ? "0" : String.valueOf(resultList.get(i).get(
                        "LAST_BALANCE"))));
                acctbk.setEncryptBalance(String.valueOf(resultList.get(i).get("ENCRYPT_BALANCE")).equals("null") ? "0" : String.valueOf(resultList.get(i).get("ENCRYPT_BALANCE")));
                acctbk.setExpireDate(Long.parseLong(String.valueOf(resultList.get(i).get("EXPIRE_DATE")).equals("null") ? "**************" : String.valueOf(resultList.get(i).get(
                        "EXPIRE_DATE"))));
                acctbk.setStartDate(Long.parseLong(String.valueOf(resultList.get(i).get("START_DATE"))));
                acctbk.setEndDate(Long.parseLong(String.valueOf(resultList.get(i).get("END_DATE")).equals("null") ? "**************" : String.valueOf(resultList.get(i).get(
                        "END_DATE"))));
                acctbk.setStatus(Short.parseShort(String.valueOf(resultList.get(i).get("STATUS")).equals("null") ? "-1" : String.valueOf(resultList.get(i).get("STATUS"))));
                acctbk.setUnit(Short.parseShort(String.valueOf(resultList.get(i).get("UNIT")).equals("null") ? "-1" : String.valueOf(resultList.get(i).get("UNIT"))));
                acctbk.setUserId(Long.parseLong(String.valueOf(resultList.get(i).get("JOIN_ID")).equals("null") ? "-1" : String.valueOf(resultList.get(i).get("JOIN_ID"))));
                acctbk.setVersion(String.valueOf(resultList.get(i).get("VERSION")));
                acctbkInfo.getListOfDetail().add(acctbk);
            }
            acctbkInfoList.add(acctbkInfo);
        }
        return acctbkInfoList;
    }

    public List<Object> getAcctPaymodeByAccountId(long accountId) {
        List<Object> returnList = iUserInfoManage.getAcctPaymodeByAccountId(CacheNameDef.CACHE_ACCT_PAYMODE, accountId);
        LogUtil.debug(LogProperty.LOGTYPE_CALL, TAG + "getAcctPaymodeByAccountId....accountId=[" + accountId + "]");
        if (returnList == null || returnList.isEmpty()) {

            LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "根据accountId[" + accountId + "]获取[" + CacheNameDef.CACHE_ACCT_PAYMODE
                    + "]表记录失败，未找到账户信息");

        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_DETAIL,
                    TAG + "根据accountId[" + accountId + "]获取[" + CacheNameDef.CACHE_ACCT_PAYMODE + "]表记录数=" + (returnList == null ? null : returnList.size()));
        }

        return returnList;
    }


    public List<AcctAccountInfo> getAcctAccountInfoByAccountId(long accountId) {
        //List<Object> returnList = MontnetCdrServer.getIgniteSpecialDao().getByKey(CacheNameDef.CACHE_ACCT_ACCOUNT, accountId);
        List<AcctAccountInfo> returnList = new ArrayList<>();
        try {

            //  private static String queryAcctAccountByAccountIdSql = "select account_id,customer_id,account_type,pay_mode,status,cc_template_id,to_char(change_date,'yyyymmddhh24miss') as change_date, " +
            //            " remark,account_name,pointpaytype,join_id from acct_account where " +
            //            " account_id = ? ";

            List<Map<String, Object>> resultList = iCreateMonManage.getAcctAccountInfoByAccountId(accountId);
            if (resultList == null || resultList.isEmpty()) {
                throw new AppException("E5109", "根据accountId[" + accountId + "]获取[" + CacheNameDef.CACHE_ACCT_ACCOUNT
                        + "]表记录失败，未找到账户信息");
            }
            LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "QUERY_BY_DB根据accountId[" + accountId + "]获取acct_account表记录数：" + resultList.size());
            returnList = this.convertReturnList(resultList);
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "根据accountId[" + accountId + "]获取[" + CacheNameDef.CACHE_ACCT_ACCOUNT + "]表记录数=" + returnList.size());
            }

            return returnList;

        } catch (Exception e) {
            LogUtil.error("QUERY_BY_DB根据accountId[" + accountId + "]获取acct_account失败", e);
        }
        return returnList;
    }

    private List<AcctAccountInfo> convertReturnList(List<Map<String, Object>> resultList) {
        List<AcctAccountInfo> acctAccountList = new ArrayList<AcctAccountInfo>();
        if (resultList != null && !resultList.isEmpty()) {
            for (int i = 0; i < resultList.size(); ++i) {


                AcctAccountInfo acctAccount = new AcctAccountInfo();
                acctAccount.setAccountId(Long.parseLong(String.valueOf(resultList.get(i).get("ACCOUNT_ID"))));
                acctAccount.setCustomerId(Long.parseLong(String.valueOf(resultList.get(i).get("CUSTOMER_ID"))));
                acctAccount.setAccountType(Short.parseShort(String.valueOf(resultList.get(i).get("ACCOUNT_TYPE"))));
                acctAccount.setPayMode(Short.parseShort(String.valueOf(resultList.get(i).get("PAY_MODE"))));
                acctAccount.setChangeDate(Long.parseLong(String.valueOf(resultList.get(i).get("CHANGE_DATE")).equals("null") ? "**************" : String.valueOf(resultList.get(i).get("CHANGE_DATE"))));
                acctAccount.setPointPayType(Short.parseShort(String.valueOf(resultList.get(i).get("POINTPAYTYPE")).equals("null") ? "0" : String.valueOf(resultList.get(i).get("POINTPAYTYPE"))));
                acctAccount.setStatus(Short.parseShort(String.valueOf(resultList.get(i).get("STATUS")).equals("null") ? "0" : String.valueOf(resultList.get(i).get("STATUS"))));
                acctAccount.setCcTemplateId(Long.parseLong(String.valueOf(resultList.get(i).get("CC_TEMPLATE_ID")).equals("null") ? "0" : String.valueOf(resultList.get(i).get("CC_TEMPLATE_ID"))));
                acctAccountList.add(acctAccount);

            }
        }
        return acctAccountList;
    }

    public AcctAccountRelationInfo getAcctAccountRelationByUserId(long userId, long currentTime) {

        LogUtil.debug(LogProperty.LOGTYPE_CALL, TAG + "getAcctAccountRelationByUserId....userId=[" + userId + "]");
        List<Object> returnList = iUserInfoManage.getAcctAccountRelationByUserId(CacheNameDef.CACHE_ACCT_ACCOUNT_RELATION, userId);

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "根据userId[" + userId + "]获取[" + CacheNameDef.CACHE_ACCT_ACCOUNT_RELATION + "]表记录数="
                    + (returnList == null ? null : returnList.size()));
        }

        if (returnList == null || returnList.isEmpty()) {
            throw new AppException("E5109", "根据userId[" + userId + "]获取[" + CacheNameDef.CACHE_ACCT_ACCOUNT_RELATION
                    + "]表记录失败，未找到账户关系");
        }

        for (int i = 0; i < returnList.size(); i++) {
            //只看自己的账务关系， 不考虑代付等情况

            AcctAccountRelationInfo acctAccountRelationInfo = (AcctAccountRelationInfo) returnList.get(i);
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "acctAccountRelationInfo: " + acctAccountRelationInfo);
            }
            if (acctAccountRelationInfo.getStatus() == 0 && acctAccountRelationInfo.getStartDate() <= currentTime
                    && acctAccountRelationInfo.getEndDate() >= currentTime && acctAccountRelationInfo.getUserId() == userId) {
                return acctAccountRelationInfo;
            }


        }

        throw new AppException("E5109", "根据userId[" + userId + "]获取[" + CacheNameDef.CACHE_ACCT_ACCOUNT_RELATION
                + "]表记录失败，未找到匹配条件的账户关系");
    }

    public void startFormatChangeMonthCdr(boolean bCharge, CngUserSpData t_orderInfo, CngMcdrChargeCfgData mcdrChargeCfgDT, List<String> dtOneUserMcdrList,
                                          ParamBean paramBean, DbResultInfo dbResultInfo,
                                          CngExdSpOperDefAllData threadLocaloperInfo, CngExdSpCodeDefAllData threadLocalspInfo, Map<String, String> threadLocalMapList,
                                          List<String> threadLocalUnBillLines, List<CngMcdrData> threadLocalMcdrList, List<CngDcdrDetailData> threadLocalMcdrDetailList,
                                          List<CngDcdrDetailData> threadLocalSettleCdrList) {
        if ("0".equals(paramBean.getErrorCode())) {
            //换月收取不收费
            if (!bCharge) {
                formatCdr(t_orderInfo, mcdrChargeCfgDT, dbResultInfo, threadLocaloperInfo, threadLocalspInfo, threadLocalMapList, threadLocalUnBillLines);
                formatMBill(t_orderInfo, dtOneUserMcdrList, dbResultInfo, threadLocaloperInfo, threadLocalspInfo,
                        threadLocalMapList, threadLocalUnBillLines, threadLocalMcdrList, paramBean, threadLocalMcdrDetailList);
                dbResultInfo.setM_mcdrNumOneDay(dbResultInfo.getM_mcdrNumOneDay() + 1);
            }
        }
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, TAG + "m_nErrCode=[" + paramBean.getErrorCode() + "]");
        }

        if ("0".equals(paramBean.getErrorCode())) {
            //MCDR_ERROR_DEALDELAY				= 600; 			//延迟处理未
            paramBean.setErrorCode("600");
        }

        //格式化结算文件
        formatSettleCdr(t_orderInfo, threadLocalMapList, paramBean, threadLocalSettleCdrList, threadLocaloperInfo);
    }

    public boolean isUnbillType(CngUserSpData dtOneUserSPlist) {
        //不收费的不生成
        if (dtOneUserSPlist.getnBillFlag() != 0 && dtOneUserSPlist.getnBillFlag() != 1 && dtOneUserSPlist.getnBillFlag() != 4) {
            return false;
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "FormatSettleCdr---start]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "准备启动，格式话结算文件，dtOneUserSPlist.m_nBillFlag :" + dtOneUserSPlist.getnBillFlag() + "]");
        }
        return true;
    }


    public void formatSettleCdr(CngUserSpData dtOneUserSPlist, Map<String, String> threadLocalMapList, ParamBean paramBean, List<CngDcdrDetailData> threadLocalSettleCdrList, CngExdSpOperDefAllData threadLocaloperInfo) {
        if (!isUnbillType(dtOneUserSPlist)) {
            return;
        }

        CngDcdrDetailData mcdrDetailDT = new CngDcdrDetailData();

        Date date = new Date();
        DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        String nowtime = format.format(date);

        mcdrDetailDT.setUserId(dtOneUserSPlist.getsUserID());
        mcdrDetailDT.setBillMonth(Montnetconfig.changeMonth.get());
        //REC_TYPE_SETTLE						= 3;				//结算明细
        mcdrDetailDT.setRecType(3);
        mcdrDetailDT.setBusiType(dtOneUserSPlist.getsSpType());
        mcdrDetailDT.setSpCode(dtOneUserSPlist.getsSpID());
        mcdrDetailDT.setOperCode(dtOneUserSPlist.getsSpBizID());
        mcdrDetailDT.setChargeFee(threadLocaloperInfo.getnFEE());
        mcdrDetailDT.setDomainId(dtOneUserSPlist.getsDoMainID());

        mcdrDetailDT.setServiceNumber(threadLocalMapList.get("SERVICE_NUMBER"));
        mcdrDetailDT.setOrderStartDate(dtOneUserSPlist.getsStartDate());
        mcdrDetailDT.setOrderEndDate(dtOneUserSPlist.getsEndDate());
        if (null != threadLocalMapList.get("STATUS")) {
            mcdrDetailDT.setUserStatus(Integer.parseInt(threadLocalMapList.get("STATUS")));
        }

        mcdrDetailDT.setErrCode(Integer.parseInt(paramBean.getErrorCode()));
        mcdrDetailDT.setCreateDate(nowtime);
        mcdrDetailDT.setChargeType((int) Montnetconfig.getChargeType());
        mcdrDetailDT.setOrderId(dtOneUserSPlist.getOrderId());
        mcdrDetailDT.setCityId(Montnetconfig.getCityID());
        mcdrDetailDT.setBydayMode(1);

        if (null == threadLocalMapList.get("SERVICE_NUMBER") || "".equals(threadLocalMapList.get("SERVICE_NUMBER")) || "null".equals(threadLocalMapList.get("SERVICE_NUMBER"))) {
            LogUtil.error("[垃圾数据用户都销户了 还有订购存在 record::" + mcdrDetailDT);
            return;
        }
        threadLocalSettleCdrList.add(mcdrDetailDT);
        return;
    }

    public void formatMBill(CngUserSpData dtOneUserSPlist, List<String> dtOneUserMcdrList, DbResultInfo dbResultInfo, CngExdSpOperDefAllData threadLocaloperInfo,
                            CngExdSpCodeDefAllData threadLocalspInfo, Map<String, String> threadLocalMapList,
                            List<String> threadLocalUnBillLines, List<CngMcdrData> threadLocalMcdrList, ParamBean paramBean, List<CngDcdrDetailData> threadLocalMcdrDetailList) {
        CngMcdrData mcdrDT = new CngMcdrData();

        //用户编号
        mcdrDT.setSzUserID(String.valueOf(dtOneUserSPlist.getsUserID()));
        //CMCC业务代码
        mcdrDT.setSzBusiType(dtOneUserSPlist.getsSpType());
        //企业代码
        mcdrDT.setSzSpCode(dtOneUserSPlist.getsSpID());
        //业务代码
        mcdrDT.setSzOperCode(dtOneUserSPlist.getsSpBizID());
        mcdrDT.setnFee(threadLocaloperInfo.getnFEE());


        if (dtOneUserSPlist.getnBillFlag() == 0 || dtOneUserSPlist.getnBillFlag() == 1 || dtOneUserSPlist.getnBillFlag() == 4) {
            mcdrDT.setnIsCharge(1);
        } else {
            mcdrDT.setnIsCharge(0);
        }

        mcdrDT.setnChargeType(Integer.parseInt(String.valueOf(Montnetconfig.getChargeType())));

        if (2 == Montnetconfig.getnLoadBillSource()) {
            CngDcdrDetailData mcdrDetailDT = new CngDcdrDetailData();

            Date date = new Date();
            DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
            String nowtime = format.format(date);

            mcdrDetailDT.setUserId(dtOneUserSPlist.getsUserID());
            mcdrDetailDT.setBillMonth(Montnetconfig.changeMonth.get());

            if (1 == mcdrDT.getnIsCharge()) {
                //REC_TYPE_CHARGE						= 1;        //收费明细SP
                mcdrDetailDT.setRecType(1);
            } else {
                //REC_TYPE_UNCHARGE					= 2;        //不收费明细SP
                mcdrDetailDT.setRecType(2);
            }

            mcdrDetailDT.setBusiType(dtOneUserSPlist.getsSpType());
            mcdrDetailDT.setSpCode(dtOneUserSPlist.getsSpID());
            mcdrDetailDT.setOperCode(dtOneUserSPlist.getsSpBizID());
            mcdrDetailDT.setChargeFee(threadLocaloperInfo.getnFEE());
            mcdrDetailDT.setDomainId(dtOneUserSPlist.getsDoMainID());
            mcdrDetailDT.setServiceNumber(threadLocalMapList.get("SERVICE_NUMBER"));
            mcdrDetailDT.setOrderStartDate(dtOneUserSPlist.getsStartDate());
            mcdrDetailDT.setOrderEndDate(dtOneUserSPlist.getsEndDate());
            if (null != threadLocalMapList.get("STATUS")) {
                mcdrDetailDT.setUserStatus(Integer.parseInt(threadLocalMapList.get("STATUS")));
            }

            mcdrDetailDT.setErrCode(Integer.parseInt(paramBean.getErrorCode()));
            mcdrDetailDT.setCreateDate(nowtime);
            mcdrDetailDT.setChargeType((int) Montnetconfig.getChargeType());
            mcdrDetailDT.setOrderId(dtOneUserSPlist.getOrderId());
            mcdrDetailDT.setCityId(Montnetconfig.getCityID());
            if (StringUtils.isNotBlank(threadLocaloperInfo.getBydayMode())) {
                mcdrDetailDT.setBydayMode(Integer.valueOf(threadLocaloperInfo.getBydayMode()));
            }
            mcdrDetailDT.setCycleTimes(dtOneUserSPlist.getCycleTimes());

            threadLocalMcdrDetailList.add(mcdrDetailDT);
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "user_id:" + mcdrDT.getSzUserID() + "][busi_type:" + mcdrDT.getSzBusiType() + "][sp_code:" +
                    mcdrDT.getSzSpCode() + "][oper_code:" + mcdrDT.getSzOperCode() + "][fee:" + mcdrDT.getnFee() + "]");
        }

        StringBuffer str_tmp = new StringBuffer();
        str_tmp.append(mcdrDT.getSzSpCode()).append("|");
        str_tmp.append(mcdrDT.getSzOperCode()).append("|");
        str_tmp.append(mcdrDT.getnIsCharge()).append("|");
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "add user_id:" + mcdrDT.getSzUserID() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "add data:" + str_tmp.toString() + "]");
        }
        dtOneUserMcdrList.add(str_tmp.toString());
        return;
    }


    public void formatCdr(CngUserSpData dtOneUserSPlist, CngMcdrChargeCfgData mcdrChargeCfgDT, DbResultInfo dbResultInfo, CngExdSpOperDefAllData threadLocaloperInfo,
                          CngExdSpCodeDefAllData threadLocalspInfo, Map<String, String> threadLocalMapList,
                          List<String> threadLocalUnBillLines) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "--start.FormatCdr----]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "开始生成该用户的包月费文件,user_id:" + dtOneUserSPlist.getsUserID() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "mcdrChargeCfgDT->m_strFileName:" + mcdrChargeCfgDT.getsFileName() + "]");
        }
        StringBuffer t_chSeqNum = new StringBuffer();
        StringBuffer cBuf = new StringBuffer();

        StringBuffer m_sFileNo = new StringBuffer();
        StringBuffer m_sAuditInfo = new StringBuffer();

        takeFileNoandAuditInfo(dtOneUserSPlist, m_sFileNo, mcdrChargeCfgDT, m_sAuditInfo, threadLocaloperInfo, dbResultInfo);

        boolean[] isFindCharge = new boolean[1];
        isFindCharge[0] = false;

        // 将 小周期次数 和 包天模式填写在标准话单xxx字段中。  1-一次性费用，2-动态月
        Object[] params = new Object[2];
        params[0] = dtOneUserSPlist.getCycleTimes() > 0 ? 2 : 1;
        params[1] = dtOneUserSPlist.getCycleTimes();

        buildMobileMallSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge, params, threadLocaloperInfo, threadLocalspInfo, threadLocalMapList, dbResultInfo, mcdrChargeCfgDT);
        buildMagicHeSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge, params, threadLocaloperInfo, threadLocalspInfo, threadLocalMapList, dbResultInfo, mcdrChargeCfgDT);
        buildMiguSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge, params, threadLocaloperInfo, threadLocalspInfo, threadLocalMapList, dbResultInfo, mcdrChargeCfgDT);
        buildNewGpSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge, params, threadLocaloperInfo, threadLocalspInfo, threadLocalMapList, dbResultInfo, mcdrChargeCfgDT);
        buildCmSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge, threadLocaloperInfo, threadLocalspInfo, threadLocalMapList, dbResultInfo, mcdrChargeCfgDT);

        if (!isFindCharge[0]) {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "不需要处理。isFindCharge：" + isFindCharge + "]");
            }
            return;
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtOneUserSPlist.getnBillFlag():" + dtOneUserSPlist.getnBillFlag() + "]");
        }

        if (dtOneUserSPlist.getnBillFlag() == 0 || dtOneUserSPlist.getnBillFlag() == 1 || dtOneUserSPlist.getnBillFlag() == 4) {
            mcdrChargeCfgDT.setsLine(cBuf.toString());
        } else {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "cBuf:" + cBuf.toString() + "]");
            }
            threadLocalUnBillLines.add(cBuf.toString());
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtUnBillLines.size():" + threadLocalUnBillLines.size() + "]");
        }

    }

    public void buildMobileMallSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge,
                                     Object[] params, CngExdSpOperDefAllData threadLocaloperInfo, CngExdSpCodeDefAllData threadLocalspInfo, Map<String, String> threadLocalMapList, DbResultInfo dbResultInfo, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        //移动商城
        if ("008011".equals(threadLocaloperInfo.getStrSERVTYPE())) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().substring(4));
            t_chSeqNum.append("00009");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.m_ptSeqnum));

            m_sourceType = "A8";
            t_callType = "";

            cBuf.append(m_sourceType).append(COMMA_STR);
            cBuf.append(t_callType).append(COMMA_STR);
            cBuf.append(String.valueOf(t_chSeqNum)).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR);
            cBuf.append("00009").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append(dbResultInfo.t_chSysDate).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append(dbResultInfo.t_chSysDate).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR);
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("250").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("07").append(COMMA_STR);
            cBuf.append("01").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("00").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(params[0]).append(COMMA_STR);
            cBuf.append("").append(params[1]).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(m_sFileNo).append(COMMA_STR);
            cBuf.append("000").append(COMMA_STR);
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR);
            cBuf.append(m_sAuditInfo).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getOrderId());
            isFindCharge[0] = true;
        }
    }

    public void buildMagicHeSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, Object[] params,
                                  CngExdSpOperDefAllData threadLocaloperInfo, CngExdSpCodeDefAllData threadLocalspInfo, Map<String, String> threadLocalMapList, DbResultInfo dbResultInfo, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        if (threadLocaloperInfo.getStrSERVTYPE().equals("090532")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);

            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.m_ptSeqnum));

            m_sourceType = "t0";
            t_callType = "02";

            cBuf.append(m_sourceType).append(COMMA_STR);
            cBuf.append(t_callType).append(COMMA_STR);
            cBuf.append(t_chSeqNum.toString()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(dbResultInfo.t_chSysDate).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append(dbResultInfo.t_chSysDate).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR);
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("250").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT);
            cBuf.append("01").append(COMMA_STR);
            cBuf.append("07").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR);
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsProdInstanceID()).append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("03").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(params[0]).append(COMMA_STR);
            cBuf.append("").append(params[1]).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(m_sFileNo).append(COMMA_STR);
            cBuf.append("000").append(COMMA_STR);
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR);
            cBuf.append(m_sAuditInfo).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getOrderId());
            isFindCharge[0] = true;
        }
    }

    public void buildMiguSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, Object[] params,
                               CngExdSpOperDefAllData threadLocaloperInfo, CngExdSpCodeDefAllData threadLocalspInfo, Map<String, String> threadLocalMapList, DbResultInfo dbResultInfo, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        if (threadLocaloperInfo.getStrSERVTYPE().equals("090526")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);

            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.m_ptSeqnum));

            m_sourceType = "r8";
            t_callType = "02";

            cBuf.append(m_sourceType).append(COMMA_STR);
            cBuf.append(t_callType).append(COMMA_STR);
            cBuf.append(t_chSeqNum.toString()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(dbResultInfo.t_chSysDate).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append(dbResultInfo.t_chSysDate).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR);
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("250").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT);
            cBuf.append("01").append(COMMA_STR);
            cBuf.append("07").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR);
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsProdInstanceID()).append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("03").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(params[0]).append(COMMA_STR);
            cBuf.append("").append(params[1]).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(m_sFileNo).append(COMMA_STR);
            cBuf.append("000").append(COMMA_STR);
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR);
            cBuf.append(m_sAuditInfo).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getOrderId());
            isFindCharge[0] = true;
        }
    }

    public void buildNewGpSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, Object[] params,
                                CngExdSpOperDefAllData threadLocaloperInfo, CngExdSpCodeDefAllData threadLocalspInfo, Map<String, String> threadLocalMapList, DbResultInfo dbResultInfo, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        if (threadLocaloperInfo.getStrSERVTYPE().equals("008013")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);

            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.m_ptSeqnum));

            m_sourceType = "r6";
            t_callType = "02";

            cBuf.append(m_sourceType).append(COMMA_STR);
            cBuf.append(t_callType).append(COMMA_STR);
            cBuf.append(t_chSeqNum.toString()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(dbResultInfo.t_chSysDate).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append(dbResultInfo.t_chSysDate).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR);
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("250").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT);
            cBuf.append("01").append(COMMA_STR);
            cBuf.append("07").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR);
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("03").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(params[0]).append(COMMA_STR);
            cBuf.append("").append(params[1]).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(m_sFileNo).append(COMMA_STR);
            cBuf.append("000").append(COMMA_STR);
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR);
            cBuf.append(m_sAuditInfo).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getOrderId());
            isFindCharge[0] = true;
        }
    }

    public void buildCmSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge,
                             CngExdSpOperDefAllData threadLocaloperInfo, CngExdSpCodeDefAllData threadLocalspInfo, Map<String, String> threadLocalMapList, DbResultInfo dbResultInfo, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        //MN话单 ， 清单分发已经配了 输入话单69 ，68,70 对应 营销折扣id, 包天模式，小周期次数。对应包月费的话单是50，
        if ("001102".equals(threadLocaloperInfo.getStrSERVTYPE())) {
            String m_sourceType = null;
            String t_callType = null;
            String strStartTime = "235958";

            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00001");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.m_ptSeqnum));

            if (threadLocalspInfo.getStrSpAttr().equals("G")) //全网                                         SPATTR_DOM  							= "G"; 			//全网SP
            {
                if (threadLocalspInfo.getnProvCode() == 250) {//本省全网业务
                    m_sourceType = "I";
                    t_callType = "02";
                } else //非本省全网业务
                {
                    if (threadLocaloperInfo.getStrSERVTYPE().equals("001104")) {
                        m_sourceType = "A1";
                        t_callType = "12";
                    }
                    //add by snail for REQ_NGBOSS2014_09139_2  新增12582和工作 090522
                    else if (threadLocaloperInfo.getStrSERVTYPE().equals("090522") || threadLocaloperInfo.getStrSERVTYPE().equals("090525")) {
                        m_sourceType = "r3";
                        t_callType = ""; //经过与计费确认，call_type置为空
                    } else {
                        m_sourceType = "2";
                        t_callType = "12";
                    }
                }
            } else if (threadLocalspInfo.getStrSpAttr().equals("L"))             //SPATTR_PROV 							= "L"; 			//本地SP
            {
                m_sourceType = "I";
                t_callType = "02";
            }

            cBuf.append(m_sourceType).append(COMMA_STR);
            cBuf.append(t_callType).append(COMMA_STR);
            cBuf.append(String.valueOf(t_chSeqNum)).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append(dbResultInfo.t_chSysDate).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append(dbResultInfo.t_chSysDate).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append(0).append(COMMA_STR);
            cBuf.append(0).append(COMMA_STR);
            cBuf.append(0).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR);
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("00001").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("250").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("07").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(m_sFileNo).append(COMMA_STR);
            cBuf.append("000").append(COMMA_STR);
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR);
            cBuf.append(m_sAuditInfo).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getOrderId());
            isFindCharge[0] = true;
        }
    }

    private void appendInstanceId(StringBuffer cBuf, CngUserSpData dtOneUserSPlist, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        if (Montnetconfig.getChargeType() == 1 && "GP".equals(mcdrChargeCfgDT.getsFILENAMEPRIX())) {
            cBuf.append(dtOneUserSPlist.getInstanceID()).append(COMMA_STR);
        } else {
            cBuf.append("").append(COMMA_STR);
        }
    }

    public void takeFileNoandAuditInfo(CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, CngMcdrChargeCfgData mcdrChargeCfgDT, StringBuffer m_sAuditInfo,
                                       CngExdSpOperDefAllData threadLocaloperInfo, DbResultInfo dbResultInfo
    ) {
        if (dtOneUserSPlist.getnBillFlag() == 0 || dtOneUserSPlist.getnBillFlag() == 1 || dtOneUserSPlist.getnBillFlag() == 4) {
            m_sFileNo.append("3");
            m_sFileNo.append(mcdrChargeCfgDT.getsFileName().substring(1, 14));
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "mcdrChargeCfgDT.getsFileName():" + mcdrChargeCfgDT.getsFileName() + "]");
            }
            m_sFileNo.append(mcdrChargeCfgDT.getsFileName().substring(24, 29));
        } else {
            m_sFileNo.append(dbResultInfo.strUnBillFileName.substring(0, 14));
            m_sFileNo.append(dbResultInfo.strUnBillFileName.substring(24, 29));
        }

        m_sAuditInfo.append('0');
        m_sAuditInfo.append(Montnetconfig.m_strDealTime.get());
        m_sAuditInfo.append(m_sFileNo);

        //不收费文件重写 m_sAuditInfo  m_sFileNo
        if (dtOneUserSPlist.getnBillFlag() != 0 && dtOneUserSPlist.getnBillFlag() != 1 && dtOneUserSPlist.getnBillFlag() != 4) {
            m_sFileNo.delete(0, m_sFileNo.length());
            m_sFileNo.append("m_sFileNo");
            m_sAuditInfo.delete(0, m_sAuditInfo.length());
            m_sAuditInfo.append("m_sAuditInfo");
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "m_sAuditInfo:" + m_sAuditInfo + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "operInfo.m_strSERV_TYPE:" + threadLocaloperInfo.getStrSERVTYPE() + "]");
        }
    }


    public void processErrorCode(CngUserSpData t_orderInfo, CngMcdrChargeCfgData mcdrChargeCfgDT, DbResultInfo dbResultInfo,
                                 ParamBean paramBean, CngExdSpOperDefAllData threadLocaloperInfo, Map<String, String> threadLocalMapList) {


        //判断是否SP处罚业务
        if (isPunishSpServ(t_orderInfo, dbResultInfo)) {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "punish sp oper,sp_id:" + t_orderInfo.getsSpID() + "][biz_id:" + t_orderInfo.getsSpBizID() + "]");
            }
            paramBean.setErrorCode("300");
        }

        //判断用户状态是否正常
        if (!isUserStatusOK(t_orderInfo, threadLocalMapList)) {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "该用户状态不正常]");
            }
            paramBean.setErrorCode("100");
        }


    }

    public boolean isUserStatusOK(CngUserSpData dtOneUserSPlist, Map<String, String> threadLocalMapList) {
        if (!getUsersInfoByUserIdfromcoherence(dtOneUserSPlist.getsUserID(), threadLocalMapList)) {
            return false;
        } else {
            //用户状态不正常
            if (!"10".equals(threadLocalMapList.get("STATUS"))) {
                //非全月停机
                if (("30".equals(threadLocalMapList.get("STATUS")) || "31".equals(threadLocalMapList.get("STATUS")))
                        && Long.parseLong(threadLocalMapList.get("STOP_DATE")) >= Long.parseLong((String.valueOf(Montnetconfig.changeMonth.get()) + "01000000"))) {
                    return true;
                } else {
                    return false;
                }
            }
        }

        return true;
    }

    public boolean getUsersInfoByUserIdfromcoherence(long nUserID, Map<String, String> threadLocalMapList) {
        try {
            List<UserInfo> returnList = iUserInfoManage.getUserByUserId(CacheNameDef.CACHE_USERS, nUserID);

            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, "[" + TAG + "根据key:" + nUserID + "查询" + CacheNameDef.CACHE_USERS + "表][获取记录数:" + (returnList == null ? null : returnList.size() + "]")
                        + "]");
            }

            if (returnList != null && returnList.size() > 0) {
                threadLocalMapList.put("STOP_DATE", String.valueOf((returnList.get(0)).getStopDate()));
                threadLocalMapList.put("STATUS", String.valueOf((returnList.get(0)).getStatus()));
                threadLocalMapList.put("IMSI", String.valueOf((returnList.get(0)).getImsi()));
                threadLocalMapList.put("SERVICE_NUMBER", String.valueOf((returnList.get(0)).getServiceNumber()));
                resetUserInfo(nUserID, threadLocalMapList);
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            LogUtil.error("根据key:" + nUserID + "查询" + CacheNameDef.CACHE_USERS + "表异常，查询数据失败！]", e);
            return false;
        }
    }

    public void resetUserInfo(long nUserID, Map<String, String> threadLocalMapList) {
        if ("0".equals(threadLocalMapList.get("STOP_DATE")) || null == threadLocalMapList.get("STOP_DATE")) {
            threadLocalMapList.put("STOP_DATE", "0");
        }

        if ("".equals(threadLocalMapList.get("STATUS"))) {
            threadLocalMapList.put("STATUS", "0");
        }

        if ("0".equals(threadLocalMapList.get("IMSI")) || null == threadLocalMapList.get("IMSI")) {
            threadLocalMapList.put("IMSI", "");
        }


        LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "~user_id=" + nUserID + "~STOP_DATE=" + threadLocalMapList.get("STOP_DATE") + "~IMSI=" + threadLocalMapList.get("IMSI")
                + "~SERVICE_NUMBER=" + threadLocalMapList.get("SERVICE_NUMBER") + "STATUS=" + threadLocalMapList.get("STATUS"));

    }

    public boolean isPunishSpServ(CngUserSpData dtOneUserSPlist, DbResultInfo dbResultInfo) {
        if (dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()) == null) {
            return false;
        }

        for (int row = 0; row < dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).size(); row++) {
            if (isSpPunishEqual(row, dtOneUserSPlist, dbResultInfo)) {
                if (dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getnPUNISHTYPE() == 3)  //停业务  SPSERV_STOPSERV  3
                {
                    return true;
                } else if (dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getnPUNISHTYPE() == 2)  //SPSERV_STOPBILL  停计费 2
                {
                    return isNeedStopBill(dtOneUserSPlist, row, dbResultInfo);
                }
            }
        }

        return false;
    }

    public boolean isNeedStopBill(CngUserSpData dtOneUserSPlist, int row, DbResultInfo dbResultInfo) {
        //判断天数是否>=r_dayNum
        //计算当月有效天数
        if (getPunishValidDayNum(dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getsSTARTDATE(), dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getsENDDATE()) >= 10)  //处罚阀值:天  10  MCDR_PUNISHSP_DAYNUM
        {
            return true;
        } else {
            return false;
        }
    }

    public int getPunishValidDayNum(String sBeginDate, String sEndDate) {
        int nDayNum = 0;
        long nMonthDay, nBeginDay, nEndDay;

        nBeginDay = Long.parseLong(sBeginDate.substring(0, 7));
        nEndDay = Long.parseLong(sEndDate.substring(0, 7));
        nMonthDay = Montnetconfig.changeMonth.get() * 100 + 1;

        if (nMonthDay < nBeginDay) {
            nMonthDay = nBeginDay;
        }

        if (nEndDay >= nMonthDay) {
            //当月
            if (nEndDay / 100 == Montnetconfig.changeMonth.get()) {
                nDayNum = (int) ((nEndDay % 100) - (nMonthDay % 100) + 1);
            } else {
                nDayNum = 31;
            }
        }


        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "get punish valid day,nDayNum:" + nDayNum + "][begin:" + nMonthDay + "][end:" + nEndDay + "]");


        return nDayNum;
    }

    public boolean isSpPunishEqual(int row, CngUserSpData dtOneUserSPlist, DbResultInfo dbResultInfo) {
        if (dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getsSPID().equals(dtOneUserSPlist.getsSpID()) &&
                (dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getsSPBIZID().equals(dtOneUserSPlist.getsSpBizID()) || "*".equals(dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getsSPBIZID()))) {
            return true;
        }
        return false;
    }

    //提交业务文件
    public void fileCommit() throws Exception {
        //提交每个业务文件接口
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---FileCommit]");
        }

        //提交鉴权失败记录
        buidFailAuth(StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get());

        buildAllChargeFile();
        //提交给结算的文件接口
        if (Montnetconfig.bMonthSwitch.get()) {
            if (StreamCreateMontnetCdrProcess.dtSettleCdrListLHZQ.get().size() > Montnetconfig.getnMaxSettleFileNum()) {
                //写数据库
                if (2 == Montnetconfig.getnLoadBillSource()) {
                    try {

                        iCreateMonManage.insertValueToTableDcdrDetail(StreamCreateMontnetCdrProcess.dtSettleCdrListLHZQ.get());
                    } catch (Exception e1) {
                        LogUtil.error(LogProperty.LOGTYPE_DETAIL, e1);
                        throw e1;
                    }
                    //推送指标todo
                    //pushFileNum(settleCdrNum, listSettele.size());
                    StreamCreateMontnetCdrProcess.dtMcdrDetailListLHZQ.get().clear();
                }
                StreamCreateMontnetCdrProcess.dtSettleCdrListLHZQ.get().clear();

            }
        }
    }


    public void buildAllChargeFile() {
        fileCommitBuildFile();
        //写不收费文件
        if (StreamCreateMontnetCdrProcess.dtUnBillLinesLHZQ.get().size() >= Montnetconfig.getnMaxUnBillFileNum()) {
            fileCommitBuildUnBill();
        }

        //提交已经收过费用的业务文件
        if (StreamCreateMontnetCdrProcess.dtMcdrListLHZQ.get().size() > Montnetconfig.getnMaxBillFileNum()) {
            batchMcdrDetailMemory();
        }
    }

    public void batchMcdrDetailMemory() {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_DETAIL, "[Montnetconfig.getnLoadBillSource():" + Montnetconfig.getnLoadBillSource() + "]");
        }
        if (1 == Montnetconfig.getnLoadBillSource()) {
            List<String> lineList = new ArrayList<String>();
            for (int col = 0; col < StreamCreateMontnetCdrProcess.dtMcdrListLHZQ.get().size(); col++) {
                StringBuffer sLine = new StringBuffer();
                sLine.append(StreamCreateMontnetCdrProcess.dtMcdrListLHZQ.get().get(col).getUserId()).append(COMMA_STR).append(StreamCreateMontnetCdrProcess.dtMcdrListLHZQ.get().get(col).getBusiType()).append(COMMA_STR);
                sLine.append(StreamCreateMontnetCdrProcess.dtMcdrListLHZQ.get().get(col).getSpCode()).append(COMMA_STR).append(StreamCreateMontnetCdrProcess.dtMcdrListLHZQ.get().get(col).getOperCode()).append(COMMA_STR);
                sLine.append(StreamCreateMontnetCdrProcess.dtMcdrListLHZQ.get().get(col).getChargeFee()).append(COMMA_STR).append(StreamCreateMontnetCdrProcess.dtMcdrListLHZQ.get().get(col).getnIsCharge());
                lineList.add(sLine.toString());
            }
            FileTool.WriteFile(Montnetconfig.getStrCdrTmpPath() + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().sBillFileName, "", "", lineList);
            FileTool.moveFile(Montnetconfig.getStrCdrTmpPath() + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get(), Montnetconfig.sBillFilePath + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get(), 0);

            //推送指标
            //pushFileNum(sBilledCdrNum, lineList.size());

        } else {
            indbFileControl();
        }
        flashMemory();
    }

    public void flashMemory() {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "灵活周期计费刷新流水表,内存原有流水：CngDcdrDetailData.dcdrDetailDataMap.size()" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dcdrDetailDataMap.size() + " --- CngDcdrDetailData.dcdrDetailDataMap :  " + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dcdrDetailDataMap);
            LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "需要刷新的流水  --- dtMcdrList.size():" + StreamCreateMontnetCdrProcess.dtMcdrListLHZQ.get().size() + "--- dtMcdrList：" +
                    StreamCreateMontnetCdrProcess.dtMcdrListLHZQ);
        }

        for (CngDcdrDetailData data : StreamCreateMontnetCdrProcess.dtMcdrListLHZQ.get()) {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "新增流水data： " + data);
            }
            if (StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dcdrDetailDataMap.get(data.getUserId()) == null) {
                List<CngDcdrDetailData> datas = new ArrayList<CngDcdrDetailData>();
                datas.add(data);
                StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dcdrDetailDataMap.put(data.getUserId(), datas);
            } else {
                StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dcdrDetailDataMap.get(data.getUserId()).add(data);
            }
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "内存中的流水data： " + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dcdrDetailDataMap.get(data.getUserId()));
            }
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "灵活周期计费刷新之后的流水表内存：CngDcdrDetailData.dcdrDetailDataMap.size():"
                    + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dcdrDetailDataMap.size() + " --- " + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dcdrDetailDataMap);
        }
        StreamCreateMontnetCdrProcess.dtMcdrListLHZQ.get().clear();
    }


    public void indbFileControl() {
        int deal_flag = 0;
        try {
            deal_flag = iCreateMonManage.insertValueToTableDcdrDetail(StreamCreateMontnetCdrProcess.dtMcdrDetailListLHZQ.get());
        } catch (Exception e1) {
            LogUtil.error(LogProperty.LOGTYPE_DETAIL, e1);
            throw e1;
        }


        StreamCreateMontnetCdrProcess.dtMcdrDetailListLHZQ.get().clear();
    }


    public void fileCommitBuildUnBill() {
        int x = 0;
        if (Montnetconfig.dtUnBillFilePathList.size() == 0) {
            LogUtil.warn("[" + TAG + "warning  no path ]");
            FileTool.WriteFile("", "", "ZZZZZ," + StreamCreateMontnetCdrProcess.dtUnBillLinesLHZQ.get().size(), StreamCreateMontnetCdrProcess.dtUnBillLinesLHZQ.get());
        } else {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "write UnBill File]");
            }

            List<String> dtUnBillLinesag = new ArrayList<String>();
            modifyunbilllist(dtUnBillLinesag);

            FileTool.WriteFile(Montnetconfig.dtUnBillFilePathList.get(0) + "/" + StreamCreateMontnetCdrProcess.strUnBillFileName.get(), "", "ZZZZZ," + dtUnBillLinesag.size(), dtUnBillLinesag);
            //向其他路径拷贝文件
            for (x = 1; x < Montnetconfig.dtUnBillFilePathList.size(); x++) {
                FileTool.copyFile(Montnetconfig.dtUnBillFilePathList.get(0) + StreamCreateMontnetCdrProcess.strUnBillFileName.get(), Montnetconfig.dtUnBillFilePathList.get(x) + StreamCreateMontnetCdrProcess.strUnBillFileName.get(), 1);
            }

            //推送指标todo
            // pushFileNum(unbillCdrNum, dtUnBillLinesag.size());

            dtUnBillLinesag.clear();
        }
        StreamCreateMontnetCdrProcess.dtUnBillLinesLHZQ.get().clear();
    }

    public void modifyunbilllist(List<String> dtUnBillLinesag) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "before+dtUnBillLines.size():" + StreamCreateMontnetCdrProcess.dtUnBillLinesLHZQ.get().size() + "]");
        }

        //重新生成不收费文件
        genUnBillFileName();

        StringBuffer m_sFileNo = new StringBuffer();
        StringBuffer m_sAuditInfo = new StringBuffer();

        m_sFileNo.append(StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().substring(0, 14));
        m_sFileNo.append(StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().substring(24, 29));

        m_sAuditInfo.append('0');
        m_sAuditInfo.append(Montnetconfig.m_strDealTime.get());
        m_sAuditInfo.append(m_sFileNo);

        for (int row = 0; row < StreamCreateMontnetCdrProcess.dtUnBillLinesLHZQ.get().size(); row++) {
            String str = StreamCreateMontnetCdrProcess.dtUnBillLinesLHZQ.get().get(row).replace("m_sFileNo", m_sFileNo.toString());
            str = str.replace("m_sAuditInfo", m_sAuditInfo);
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "str~str~~str:" + str + "]");
            }
            dtUnBillLinesag.add(str);

        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "after+dtUnBillLines.size():" + StreamCreateMontnetCdrProcess.dtUnBillLinesLHZQ.get().size() + "]");
        }

        return;
    }

    public void genUnBillFileName() {
        String cMmDd = null;
        String cDataTime = null;

        if (StreamCreateMontnetCdrProcess.dtUnBillLinesLHZQ.get() != null) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().delete(0, StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().length());
        }

        if (StreamCreateMontnetCdrProcess.nFileNoLHZQ.get() == 0) {
            if (Montnetconfig.getChargeType() == 1) {
                StreamCreateMontnetCdrProcess.nFileNoLHZQ.set(1L);
            } else if (Montnetconfig.getChargeType() == 2) {
                StreamCreateMontnetCdrProcess.nFileNoLHZQ.set(0L);
            }
        }

        StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("3");
        StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append(Montnetconfig.getStrUnBillPrefix());
        StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("P");

        pinJieUnbillFileName();

        StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append(String.format("%03d", Montnetconfig.channelnum.get()));
        StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append(".");

        Date date = new Date();
        DateFormat format = new SimpleDateFormat("MMdd");
        DateFormat format2 = new SimpleDateFormat("MMddHHmm");
        cMmDd = format.format(date);
        cDataTime = format2.format(date);

        StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append(cMmDd);
        StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append(".");
        StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append(cDataTime);
        StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append(".");

        StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append(String.format("%06d", StreamCreateMontnetCdrProcess.nFileNoLHZQ.get()));

        if (StreamCreateMontnetCdrProcess.nFileNoLHZQ.get() >= 999998) {
            if (Montnetconfig.getChargeType() == 1) {
                StreamCreateMontnetCdrProcess.nFileNoLHZQ.set(1L);
            } else if (Montnetconfig.getChargeType() == 2) {
                StreamCreateMontnetCdrProcess.nFileNoLHZQ.set(0L);
            }
        } else {
            StreamCreateMontnetCdrProcess.nFileNoLHZQ.set(StreamCreateMontnetCdrProcess.nFileNoLHZQ.get() + 4);


        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-------start MontnetCdrDB.GenerateFileName() strUnBillFileName:" + StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().toString() + "]");
        }
    }


    public void pinJieUnbillFileName() {
        if (Montnetconfig.getCityID() == 11) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("E");
        } else if (Montnetconfig.getCityID() == 12) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("H");
        } else if (Montnetconfig.getCityID() == 13) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("N");
        } else if (Montnetconfig.getCityID() == 14) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("A");
        } else if (Montnetconfig.getCityID() == 15) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("G");
        } else if (Montnetconfig.getCityID() == 16) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("C");
        } else if (Montnetconfig.getCityID() == 17) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("D");
        } else if (Montnetconfig.getCityID() == 18) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("L");
        }

        pinJieUnbillFileNameThen();
    }

    public void pinJieUnbillFileNameThen() {
        if (Montnetconfig.getCityID() == 19) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("B");
        } else if (Montnetconfig.getCityID() == 20) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("F");
        } else if (Montnetconfig.getCityID() == 21) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("M");
        } else if (Montnetconfig.getCityID() == 22) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("J");
        } else if (Montnetconfig.getCityID() == 23) {
            StreamCreateMontnetCdrProcess.strUnBillFileNameLHZQ.get().append("K");
        }
    }


    public void fileCommitBuildFile() {
        int i = 0;
        double pushFileNum = 0d;

        for (i = 0; i < StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.size(); ++i) {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "cngMcdrChargeCfgDT.dtMcdrChargeCfgList.get(i).getM_vFileList().size():" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getM_vFileList().size() + "]");
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "iiiiiiii:" + i + "]");
            }
            if (StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getM_vFileList().size() >= StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getnFILEMAXNUM())   //达到该业务一个文件的上限
            {
                pushFileNum = pushFileNum + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getM_vFileList().size();
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "coming????" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getM_vFileList().size() + "," + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getnFILEMAXNUM() + "]");
                }
                StringBuffer sFileName = new StringBuffer();

                //写文件
                FileTool.WriteFile(Montnetconfig.getStrCdrTmpPath() + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getsFileName(), "", "ZZZZZ," + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getM_vFileList().size(), StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getM_vFileList());

                FileTool.copyFile(Montnetconfig.getStrCdrTmpPath() + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getsFileName(), Montnetconfig.getStrCdrBakPath() + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getsFileName(), 1);

                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "备份清单文件成功,strSouFile:" + Montnetconfig.getStrCdrTmpPath() + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getsFileName() +
                        " ][strTarFile:" + Montnetconfig.getStrCdrBakPath() + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getsFileName() + "]");

                //输出文件
                FileTool.moveFile(Montnetconfig.getStrCdrTmpPath() + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getsFileName(), Montnetconfig.getStrCdrOutPath() + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getsFileName(), 0);

                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "输出清单文件成功,strTarFile:" + Montnetconfig.getStrCdrOutPath() + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getsFileName() + "]");

                StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).getM_vFileList().clear();

                //生成文件名
                generateFileName(StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i), sFileName);
                StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().dtMcdrChargeCfgList.get(i).setsFileName(sFileName.toString());
            }
        }

        //推送话单数量指标todo
        //pushFileNum(chargeCdrNum, pushFileNum);

    }

    public void generateFileName(CngMcdrChargeCfgData dtSPChargeCfg, StringBuffer sFileName) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---GenerateFileName]");
        }

        sFileName.append("");
        sFileName.append(Montnetconfig.getStrCdrPrefix());
        sFileName.append(dtSPChargeCfg.getsFILENAMEPRIX());

        resetFileNameByCityCode(dtSPChargeCfg, sFileName);
        resetFileNameByCityLastCode(sFileName);

        sFileName.append(String.format("%03d", Montnetconfig.channelnum.get()));

        sFileName.append(".");

        Date date = new Date();
        DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        String cSysdate = format.format(date);

        sFileName.append(cSysdate.substring(4, 8));
        sFileName.append(".");
        sFileName.append(cSysdate.substring(4, 12));
        sFileName.append(".");
        sFileName.append(String.format("%06d", StreamCreateMontnetCdrProcess.nFileNoLHZQ.get()));

        if (StreamCreateMontnetCdrProcess.nFileNoLHZQ.get() >= 999998) {
            if (Montnetconfig.getChargeType() == 1) {
                StreamCreateMontnetCdrProcess.nFileNoLHZQ.set(1L);
            } else if (Montnetconfig.getChargeType() == 2) {
                StreamCreateMontnetCdrProcess.nFileNoLHZQ.set(0L);
            }
        } else {
            StreamCreateMontnetCdrProcess.nFileNoLHZQ.set(StreamCreateMontnetCdrProcess.nFileNoLHZQ.get() + 4);

        }
        return;
    }

    public void resetFileNameByCityCode(CngMcdrChargeCfgData dtSPChargeCfg, StringBuffer sFileName) {
        if (dtSPChargeCfg.getnBusinessType() == 0) {
            sFileName.append("D");
        } else {
            sFileName.append("P");
        }

        if (Montnetconfig.getCityID() == 11) {
            sFileName.append("E");
        } else if (Montnetconfig.getCityID() == 12) {
            sFileName.append("H");
        } else if (Montnetconfig.getCityID() == 13) {
            sFileName.append("N");
        } else if (Montnetconfig.getCityID() == 14) {
            sFileName.append("A");
        } else if (Montnetconfig.getCityID() == 15) {
            sFileName.append("G");
        } else if (Montnetconfig.getCityID() == 16) {
            sFileName.append("C");
        } else if (Montnetconfig.getCityID() == 17) {
            sFileName.append("D");
        }
    }

    public void resetFileNameByCityLastCode(StringBuffer sFileName) {
        if (Montnetconfig.getCityID() == 18) {
            sFileName.append("L");
        } else if (Montnetconfig.getCityID() == 19) {
            sFileName.append("B");
        } else if (Montnetconfig.getCityID() == 20) {
            sFileName.append("F");
        } else if (Montnetconfig.getCityID() == 21) {
            sFileName.append("M");
        } else if (Montnetconfig.getCityID() == 22) {
            sFileName.append("J");
        } else if (Montnetconfig.getCityID() == 23) {
            sFileName.append("K");
        }
    }

    private void buidFailAuth(DbResultInfo dbResultInfo) {
        int deal_flag = 0;
        List<CngDcdrDetailData> failAuthCdrList = StreamCreateMontnetCdrProcess.dtFailAuthCdrListLHZQ.get();
        if (CollUtil.isNotEmpty(failAuthCdrList)) {
            try {

                iCreateMonManage.insertValueToTableDcdrDetail(failAuthCdrList);
                //刷新流水表内存
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "插入鉴权失败记录，刷新流水表,内存原有流水：CngDcdrDetailData.dcdrDetailDataMap.size()" +
                            dbResultInfo.dcdrDetailDataMap.size() + " --- CngDcdrDetailData.dcdrDetailDataMap :  " + dbResultInfo.dcdrDetailDataMap);
                    LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "需要刷新的流水  --- dtMcdrList.size():" +
                            failAuthCdrList.size() + "--- dtMcdrList：" + failAuthCdrList);
                }
                for (CngDcdrDetailData data : failAuthCdrList) {
                    if (LogUtil.isDebugEnable()) {
                        LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "新增流水data： " + data);
                    }
                    if (dbResultInfo.dcdrDetailDataMap.get(data.getUserId()) == null) {
                        List<CngDcdrDetailData> datas = new ArrayList<CngDcdrDetailData>();
                        datas.add(data);
                        dbResultInfo.dcdrDetailDataMap.put(data.getUserId(), datas);
                    } else {
                        dbResultInfo.dcdrDetailDataMap.get(data.getUserId()).add(data);
                    }
                    if (LogUtil.isDebugEnable()) {
                        LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "内存中的流水data： " + dbResultInfo.dcdrDetailDataMap.get(data.getUserId()));
                    }
                }
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "刷新之后的流水表内存：CngDcdrDetailData.dcdrDetailDataMap.size():" +
                            dbResultInfo.dcdrDetailDataMap.size() + " --- " + dbResultInfo.dcdrDetailDataMap);
                }

            } catch (Exception e) {
                LogUtil.error("[插入表[DCDR_DETAIL]操作失败 该条动态鉴权重做！", e);
            }
            failAuthCdrList.clear();
        }

    }


    public void closeFile(int taskItemNum) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---LHZQCloseFile]");
        }

        //提交每个业务文件接口
        buildChargeFile();
        buildUnbillFile();


        //提交已经收过费用的业务文件
        if (StreamCreateMontnetCdrProcess.dtMcdrListLHZQ.get().size() > 0) {
            batchMcdrDetailMemory();
        }

        //提交给结算的文件接口
        indbSettleFile();


        if (LogUtil.isDebugEnable()) {
            LogUtil.debug("DTL", TAG + new String[]{"[OUT--END--END--END--END--]"});
            LogUtil.debug("DTL", TAG + new String[]{"[Montnetconfig.bMonthSwitch:" + Montnetconfig.bMonthSwitch.get() + "]"});
        }


    }

    public void indbSettleFile() {
        if (Montnetconfig.bMonthSwitch.get()) {
            List<CngDcdrDetailData> listSettle = StreamCreateMontnetCdrProcess.dtSettleCdrListLHZQ.get();
            if (listSettle.size() > 0) {
                //写数据库
                if (2 == Montnetconfig.getnLoadBillSource()) {


                    try {
                        iCreateMonManage.insertValueToTableDcdrDetail(listSettle);
                    } catch (Exception e1) {
                        LogUtil.error(LogProperty.LOGTYPE_DETAIL, e1);
                    }
                    //推送指标TODO
                    //pushFileNum(settleCdrNum, listSettle.size());
                    StreamCreateMontnetCdrProcess.dtMcdrDetailListLHZQ.get().clear();
                }
                StreamCreateMontnetCdrProcess.dtSettleCdrListLHZQ.get().clear();
            }
        }
    }


    public void buildUnbillFile() {
        int x;
        //写不收费文件
        List<String> dtUnBillLines = StreamCreateMontnetCdrProcess.dtUnBillLinesLHZQ.get();
        if (CollUtil.isNotEmpty(dtUnBillLines)) {
            if (Montnetconfig.dtUnBillFilePathList.size() == 0) {
                FileTool.WriteFile("" + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().getStrUnBillFileName(), "", "ZZZZZ," + dtUnBillLines.size(), dtUnBillLines);
            } else {
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "write UnBill File]");
                }

                List<String> dtUnBillLinesag = new ArrayList<String>();
                modifyunbilllist(dtUnBillLinesag);

                //FileTool.WriteFile(Montnetconfig.getStrCdrTmpPath() + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().getStrUnBillFileName(), "", "ZZZZZ," + dtUnBillLinesag.size(), dtUnBillLinesag);

                //向其他路径拷贝文件
                for (x = 0; x < Montnetconfig.dtUnBillFilePathList.size(); x++) {
                    String pathtemp[] = Montnetconfig.dtUnBillFilePathList.get(x).split("\\;");
                    for (int ccc = 0; ccc < pathtemp.length; ccc++) {

                        FileTool.WriteFile(pathtemp[ccc] + "/tmp/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().getStrUnBillFileName(), "", "ZZZZZ," + dtUnBillLinesag.size(), dtUnBillLinesag);
                        FileTool.moveFile(pathtemp[ccc] + "/tmp/"  + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().getStrUnBillFileName(), pathtemp[ccc].toString() + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().getStrUnBillFileName(), 0);
                        if (LogUtil.isDebugEnable()) {
                            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "strUnBillFileName=" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().getStrUnBillFileName() + "]");
                        }
                    }
                }
                //FileTool.removeFile(Montnetconfig.getStrCdrTmpPath() + "/" + StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().getStrUnBillFileName());
                dtUnBillLinesag.clear();

                //推送指标TODO
                //pushFileNum(unbillCdrNum, dtUnBillLinesag.size());
            }

            dtUnBillLines.clear();
            //生成不收费文件名
            genUnBillFileName();
        }
    }


    public void buildChargeFile() {
        int i;
        double cdrNum = 0d;

        for (i = 0; i < StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().getDtMcdrChargeCfgList().size(); i++) {
            CngMcdrChargeCfgData chargeCfgData = StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().getDtMcdrChargeCfgList().get(i);
            if (chargeCfgData.getM_vFileList().size() > 0) {
                //记录话单数量
                cdrNum = cdrNum + chargeCfgData.getM_vFileList().size();

                StringBuffer sFileName = new StringBuffer();
                //写文件
                FileTool.WriteFile(Montnetconfig.getStrCdrTmpPath() + "/" + chargeCfgData.getsFileName(), "", "ZZZZZ," + chargeCfgData.getM_vFileList().size(), chargeCfgData.getM_vFileList());
                FileTool.copyFile(Montnetconfig.getStrCdrTmpPath() + "/" + chargeCfgData.getsFileName(), Montnetconfig.getStrCdrBakPath() + "/" + chargeCfgData.getsFileName(), 1);
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "备份清单文件成功,strTarFile:" + Montnetconfig.getStrCdrBakPath() + "/" + chargeCfgData.getsFileName() + "]");
                }
                FileTool.moveFile(Montnetconfig.getStrCdrTmpPath() + "/" + chargeCfgData.getsFileName(), Montnetconfig.getStrCdrOutPath() + "/" + chargeCfgData.getsFileName(), 0);
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "输出清单文件成功,strTarFile:" + Montnetconfig.getStrCdrOutPath() + "/" + chargeCfgData.getsFileName() + "]");
                }
                StreamCreateMontnetCdrProcess.dbResultInfoLHZQ.get().getDtMcdrChargeCfgList().get(i).getM_vFileList().clear();
                //生成文件名
                generateFileName(chargeCfgData, sFileName);
                chargeCfgData.setsFileName(sFileName.toString());
            }
        }

        //推送指标TODO
        //pushFileNum(chargeCdrNum, cdrNum);
    }


}
