package com.newland.streamboss.streamCreateMontnetCdr.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.newland.boss.nlstreamboss.streamcompoment.domain.infoobject.UserInfo;
import com.newland.boss.nlstreamboss.streamcompoment.domain.infoobject.UserProductExpInfo;
import com.newland.boss.nlstreamboss.streamcompoment.domain.infoobject.UserServiceInfo;
import com.newland.boss.nlstreamboss.streamcompoment.domain.util.CacheNameDef;
import com.newland.computer.boss.bossbiz.app.appprocess.appcommon.util.GlobalFunc;
import com.newland.sri.ccp.logmgr.LogProperty;
import com.newland.streamboss.biz_createMontnetCdr.api.ICreateMonManage;
import com.newland.streamboss.biz_createMontnetCdr.api.IUserInfoManage;
import com.newland.streamboss.biz_createMontnetCdr.bo.*;
import com.newland.streamboss.biz_createMontnetCdr.dao.ICreateMonDao;
import com.newland.streamboss.streamCreateMontnetCdr.config.Montnetconfig;
import com.newland.streamboss.streamCreateMontnetCdr.entity.DbResultInfo;
import com.newland.streamboss.streamCreateMontnetCdr.entity.ParamBean;
import com.newland.streamboss.streamCreateMontnetCdr.utils.DealDataBase;
import com.newland.streamboss.utils.utils.LogUtil;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: SourceCode
 * @description:
 * @author: luoting
 * @create: 2022-02-17 15:38
 **/
@Service
public class MonthFeeService {


    public static final char COMMA_STR = ',';
    private static String TAG = "MonthFeeService======";

    @Autowired
    private CngExdService cngExdService;

    @Autowired
    IUserInfoManage iUserInfoManage;

    @Autowired
    ICreateMonManage iCreateMonManage;

    public static final int EXPRTYPE = 11;

    public boolean isCreatedMcdr(List<CngUserSpData> dtOneUserSPlist, boolean bCharge, DbResultInfo dbResultInfo) {

        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "isCreatedMcdr，dtOneUserSPlist:" + dtOneUserSPlist.size() + "]");


        //六点之后只生成特定业务的包月费
        Calendar rightNow = Calendar.getInstance();
        int nDay = rightNow.get(Calendar.DATE);
        int nHour = rightNow.get(Calendar.HOUR_OF_DAY);
        if (nHour > Montnetconfig.lastDealHour && nDay != DealDataBase.getLastDayOfMonth() && bCharge) {
            List<CngAccountBaseCfgData> baseCfgByCityIdList = cngExdService.getAccountBaseCfgByCityId(6519);

            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "六点之后只生成特定业务的包月费，accountbasecfgList::" + baseCfgByCityIdList + "]");


            if (CollUtil.isNotEmpty(baseCfgByCityIdList)) {
                Iterator<CngUserSpData> iterator = dtOneUserSPlist.iterator();
                while (iterator.hasNext()) {
                    CngUserSpData next = iterator.next();
                    boolean delFlag = true;
                    for (CngAccountBaseCfgData accountBaseCfgData : baseCfgByCityIdList) {
                        //特定业务的包月费
                        if (next.getsSpID().equals(accountBaseCfgData.getStrValueStr())
                                && next.getsSpBizID().equals(accountBaseCfgData.getStrValueRemark())) {
                            delFlag = false;
                            break;
                        }
                    }
                    if (delFlag) {
                        iterator.remove();
                    }
                }
            } else {
                dtOneUserSPlist.clear();
            }
        }


        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "isCreatedMcdr，dtOneUserSPlist:" + dtOneUserSPlist.size() + "]");


        if (dtOneUserSPlist.size() <= 0) {
            return true;
        }

        if (!dbResultInfo.getMapMcdrList().containsKey(String.valueOf(dtOneUserSPlist.get(0).getsUserID()))) {

            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "无该用户的收费记录，user_id:" + dtOneUserSPlist.get(0).getsUserID() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "MontnetCdrDB.mapMcdrList.size():" + dbResultInfo.getMapMcdrList().size() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtOneUserSPlist.get(0).getnBillFlag():" + dtOneUserSPlist.get(0).getnBillFlag() + "]");

            return false;
        }

        int nIsCharge = -1;

        for (int row = 0; row < dbResultInfo.getMapMcdrList().get(String.valueOf(dtOneUserSPlist.get(0).getsUserID())).size(); row++) {
            if (getChargeFlag(dtOneUserSPlist)) {
                nIsCharge = 1;
            } else {
                nIsCharge = 0;
            }

            if (isServiceEqual(dbResultInfo.getMapMcdrList().get(String.valueOf(dtOneUserSPlist.get(0).getsUserID())).get(row), dtOneUserSPlist.get(0), nIsCharge)) {
                return true;
            }
        }
        return false;
    }

    public boolean getChargeFlag(List<CngUserSpData> dtOneUserSPlist) {
        if (dtOneUserSPlist.get(0).getnBillFlag() == 0 || dtOneUserSPlist.get(0).getnBillFlag() == 1 || dtOneUserSPlist.get(0).getnBillFlag() == 4) {
            return true;
        }
        return false;
    }


    public boolean isServiceEqual(CngMcdrData dt, CngUserSpData userSpDt, int nIsCharge) {
        if (dt.getSzSpCode().equals(userSpDt.getsSpID()) && dt.getSzOperCode().equals(userSpDt.getsSpBizID()) && dt.getnIsCharge() == nIsCharge) {

            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "该业务已经收过了，不需要再收取][user_id:" + userSpDt.getsUserID() + "][sp_id:" + userSpDt.getsSpID() + " ][oper_code:" + userSpDt.getsSpBizID()
                    + "][billing_flag:" + userSpDt.getnBillFlag() + "][mbill.is_charge:" + dt.getnIsCharge() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "MontnetCdrDB.mapMcdrList.get(dtOneUserSPlist.get(0).getsUserID()).get(row).getSzSpCode():" + dt.getSzSpCode() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtOneUserSPlist.get(0).getsSpID()=" + userSpDt.getsSpID());
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "MontnetCdrDB.mapMcdrList.get(dtOneUserSPlist.get(0).getsUserID()).get(row).getSzOperCode():" + dt.getSzOperCode() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtOneUserSPlist.get(0).getsSpBizID()=" + userSpDt.getsSpBizID());
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "MontnetCdrDB.mapMcdrList.get(dtOneUserSPlist.get(0).getsUserID()).get(row).getnIsCharge():" + dt.getnIsCharge() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "nIsCharge:" + nIsCharge + "]");

            return true;
        }
        return false;
    }

    public void doProcess(boolean bCharge, List<CngUserSpData> dtUserSPlistONEAll, List<String> dtOneUserMcdrList, DbResultInfo dbResultInfo, List<Object[]> message) {
        int row = 0;
        int loop2 = 0;
        int col = 0;
        LogUtil.debug("[" + TAG + "doProcess接收到的消息dtUserSPlistONEAll.size():" + dtUserSPlistONEAll.size() + "]");
        for (row = 0; row < dtUserSPlistONEAll.size(); row++) {
            //initParams();
            List<CngUserSpData> dtUserSPlistONE = new ArrayList<>();
            dtUserSPlistONE.add(dtUserSPlistONEAll.get(row));
            loop2 = row;

            for (col = (row + 1); col < dtUserSPlistONEAll.size(); col++) {
                if (dtUserSPlistONEAll.get(row).equalsAll(dtUserSPlistONEAll.get(col))) {
                    dtUserSPlistONE.add(dtUserSPlistONEAll.get(col));
                    loop2 = col;
                } else {
                    break;
                }
            }
            row = loop2;

            CngMcdrChargeCfgData mcdrChargeCfgDT = new CngMcdrChargeCfgData();
            List<String> threadLocalUnBillLines = new ArrayList<>();
            List<CngMcdrData> threadLocalMcdrList = new ArrayList<>();
            List<CngMcdrDetailData> threadLocalMcdrDetailList = new ArrayList<>();
            List<CngMcdrDetailData> threadLocalSettleCdrList = new ArrayList<>();
            dealSp(dtUserSPlistONE, bCharge, mcdrChargeCfgDT, dtOneUserMcdrList, dbResultInfo, threadLocalUnBillLines, threadLocalMcdrList,
                    threadLocalMcdrDetailList, threadLocalSettleCdrList);

            Object[] outobj = new Object[8];
            outobj[0] = "normal";
            outobj[1] = bCharge;
            outobj[2] = threadLocalUnBillLines;
            outobj[3] = threadLocalMcdrList;
            outobj[4] = threadLocalMcdrDetailList;
            outobj[5] = threadLocalSettleCdrList;
            outobj[6] = mcdrChargeCfgDT;
            outobj[7] = dbResultInfo;


            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "--dtUnBillLines=--" + threadLocalUnBillLines + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "--dtMcdrList=--" + threadLocalMcdrList + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "--dtMcdrDetailList=--" + threadLocalMcdrDetailList + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "--dtSettleCdrList=--" + threadLocalSettleCdrList + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "--mcdrChargeCfgDT=--" + mcdrChargeCfgDT + "]");

            message.add(outobj);
        }
    }


    public void dealSp(List<CngUserSpData> dtUserSPlistONE, boolean bCharge, CngMcdrChargeCfgData mcdrChargeCfgDT,
                       List<String> dtOneUserMcdrList, DbResultInfo dbResultInfo, List<String> threadLocalUnBillLines, List<CngMcdrData> threadLocalMcdrList,
                       List<CngMcdrDetailData> threadLocalMcdrDetailList, List<CngMcdrDetailData> threadLocalSettleCdrList
    ) {

        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---dealSp]");


        List<String> m_fOrdTimeList = new ArrayList<>();
        CngUserSpData t_orderInfo = new CngUserSpData();
        CngExdSpCodeDefAllData threadLocalspInfo = new CngExdSpCodeDefAllData();
        CngExdSpOperDefAllData threadLocaloperInfo = new CngExdSpOperDefAllData();
        ParamBean paramBean = new ParamBean();
        paramBean.setErrorCode("0");
        Map<String, String> threadLocalMapList = new HashMap<>();

        //处理该业务
        processMcdr(dtUserSPlistONE, bCharge, mcdrChargeCfgDT, m_fOrdTimeList, t_orderInfo, dtOneUserMcdrList, dbResultInfo,
                threadLocalspInfo, threadLocaloperInfo, paramBean, threadLocalMapList, threadLocalUnBillLines,
                threadLocalMcdrList, threadLocalMcdrDetailList, threadLocalSettleCdrList);


    }


    public void processMcdr(List<CngUserSpData> dtUserSPlistONE, boolean bCharge, CngMcdrChargeCfgData mcdrChargeCfgDT, List<String> m_fOrdTimeList,
                            CngUserSpData t_orderInfo, List<String> dtOneUserMcdrList, DbResultInfo dbResultInfo,
                            CngExdSpCodeDefAllData threadLocalspInfo, CngExdSpOperDefAllData threadLocaloperInfo,
                            ParamBean paramBean, Map<String, String> threadLocalMapList, List<String> threadLocalUnBillLines,
                            List<CngMcdrData> threadLocalMcdrList, List<CngMcdrDetailData> threadLocalMcdrDetailList,
                            List<CngMcdrDetailData> threadLocalSettleCdrList) {
        long processMcdrStart = System.currentTimeMillis();
        if (!isNeedProcess(dtUserSPlistONE, mcdrChargeCfgDT, dbResultInfo, threadLocalspInfo, threadLocaloperInfo)) {
            return;
        }

        if (!isSpNeedProcess(dtUserSPlistONE, bCharge, mcdrChargeCfgDT, m_fOrdTimeList, t_orderInfo, threadLocalspInfo, dbResultInfo)) {
            return;
        }

        if (isUserExperienceDealed(t_orderInfo)){
            return;
        }

        processErrorCode(t_orderInfo, mcdrChargeCfgDT, dbResultInfo, paramBean, threadLocaloperInfo, threadLocalMapList);

        reSetErrorCode(dtUserSPlistONE, dtOneUserMcdrList, paramBean, dbResultInfo);

        //营销折扣活动判断
        Object[] marketFee = parseMarketFee(t_orderInfo, threadLocaloperInfo, dbResultInfo);


        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "marketFee :" + marketFee[1] + " marketID：" + marketFee[0] + "]");
        long processMcdrEnd = System.currentTimeMillis();
        LogUtil.debug(LogProperty.LOGTYPE_CALL, "BBBBB I AM COMING：" + t_orderInfo.getsUserID() + "[" + TAG + "m_nErrCode:" + paramBean.getErrorCode() + "]");
        if (Montnetconfig.bMonthSwitch.get()) {
            LogUtil.debug("耗时：" + (processMcdrEnd - processMcdrStart) + "ms," + "处理换月逻辑Start,m_nErrCode=" + paramBean.getErrorCode());

            startFormatChangeMonthCdr(bCharge, t_orderInfo, mcdrChargeCfgDT, dtOneUserMcdrList, marketFee, paramBean,
                    dbResultInfo, threadLocaloperInfo, threadLocalspInfo, threadLocalMapList, threadLocalUnBillLines,
                    threadLocalMcdrList, threadLocalMcdrDetailList, threadLocalSettleCdrList);
            LogUtil.debug("处理换月逻辑End");
        } else {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "不换月：" + t_orderInfo.getsUserID() + "[" + TAG + "m_nErrCode:" + paramBean.getErrorCode() + "]");
            if ("0".equals(paramBean.getErrorCode())) {
                //初始化话单
                formatCdr(t_orderInfo, mcdrChargeCfgDT, marketFee, dbResultInfo, threadLocaloperInfo,
                        threadLocalspInfo, threadLocalMapList, threadLocalUnBillLines);

                //初始化已经收过费的文件
                formatMBill(t_orderInfo, dtOneUserMcdrList, marketFee, dbResultInfo, threadLocaloperInfo, threadLocalspInfo,
                        threadLocalMapList, threadLocalUnBillLines, threadLocalMcdrList, paramBean, threadLocalMcdrDetailList);

                dbResultInfo.setM_mcdrNumOneDay(dbResultInfo.getM_mcdrNumOneDay() + 1);
            }
        }

    }

    private boolean isUserExperienceDealed(CngUserSpData t_orderInfo) {
        try {
            int billMonth = Convert.toInt(Montnetconfig.changeMonth.get());
            if (Montnetconfig.getUserExpIgniteFlag() == 0) {
                if (iCreateMonManage.getCountExpByParams(billMonth, t_orderInfo.getsUserID(), t_orderInfo.getsSpID(), t_orderInfo.getsSpBizID()) != 0) {
                    LogUtil.warn("USER_PRODUCT_EXPERENCE_B已处理过，无需再处理");
                    return true;
                }
            } else {
                List<UserProductExpInfo> list = iUserInfoManage.getExpFromIgniteByUserId(CacheNameDef.CACHE_USER_PRODUCT_EXPERIENCE, t_orderInfo.getsUserID());
                if (CollUtil.isNotEmpty(list)) {
                    LogUtil.debug("user_product_exp_size为" + list.size());
                    List<UserProductExpInfo> filtered = list.stream().filter(c -> Convert.toStr(c.getStartDate(), "0").length() >= 6 && Integer.parseInt(String.valueOf(c.getStartDate()).substring(0, 6)) == billMonth
                            && c.getSpID().equals(t_orderInfo.getsSpID())
                            && c.getSpBizID().equals(t_orderInfo.getsSpBizID())
                            &&c.getExprType()==EXPRTYPE
                            &&String.valueOf(c.getProdInstanceID()).equals(t_orderInfo.getsProdInstanceID())).collect(Collectors.toList());
                    LogUtil.debug("user_product_exp_filter_size为" + filtered.size());
                    if (filtered.size() > 0) {
                        LogUtil.warn("USER_PRODUCT_EXPERENCE_B已处理过，无需再处理");
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            LogUtil.error("USER_PRODUCT_EXPERENCE_B查询失败", e);
            return false;
        }
        return false;
    }

    public void startFormatChangeMonthCdr(boolean bCharge, CngUserSpData t_orderInfo, CngMcdrChargeCfgData mcdrChargeCfgDT, List<String> dtOneUserMcdrList, Object[] marketFee,
                                          ParamBean paramBean, DbResultInfo dbResultInfo,
                                          CngExdSpOperDefAllData threadLocaloperInfo, CngExdSpCodeDefAllData threadLocalspInfo, Map<String, String> threadLocalMapList,
                                          List<String> threadLocalUnBillLines, List<CngMcdrData> threadLocalMcdrList, List<CngMcdrDetailData> threadLocalMcdrDetailList, List<CngMcdrDetailData> threadLocalSettleCdrList) {


        if ("0".equals(paramBean.getErrorCode())) {
            //换月收取不收费
            LogUtil.debug(TAG + "m_nErrCode=[" + paramBean.getErrorCode() + ",bCharge=" + bCharge + "]");
            if (!bCharge) {
                formatCdr(t_orderInfo, mcdrChargeCfgDT, marketFee, dbResultInfo, threadLocaloperInfo, threadLocalspInfo, threadLocalMapList, threadLocalUnBillLines);
                formatMBill(t_orderInfo, dtOneUserMcdrList, marketFee, dbResultInfo, threadLocaloperInfo, threadLocalspInfo,
                        threadLocalMapList, threadLocalUnBillLines, threadLocalMcdrList, paramBean, threadLocalMcdrDetailList);
                dbResultInfo.setM_mcdrNumOneDay(dbResultInfo.getM_mcdrNumOneDay() + 1);
            }
        }


        if ("0".equals(paramBean.getErrorCode())) {
            //MCDR_ERROR_DEALDELAY				= 600; 			//延迟处理未
            paramBean.setErrorCode("600");
        }

        //格式化结算文件
        formatSettleCdr(t_orderInfo, marketFee, threadLocalMapList, paramBean, threadLocalSettleCdrList);
    }

    public void formatSettleCdr(CngUserSpData dtOneUserSPlist, Object[] marketFee, Map<String, String> threadLocalMapList, ParamBean paramBean, List<CngMcdrDetailData> threadLocalSettleCdrList) {
        if (!isUnbillType(dtOneUserSPlist)) {
            return;
        }

        CngMcdrDetailData mcdrDetailDT = new CngMcdrDetailData();

        Date date = new Date();
        DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        String nowtime = format.format(date);

        mcdrDetailDT.setStrUserID(String.valueOf(dtOneUserSPlist.getsUserID()));
        mcdrDetailDT.setnBillMonth(Montnetconfig.changeMonth.get());
        //REC_TYPE_SETTLE						= 3;				//结算明细
        mcdrDetailDT.setnRecType(3);
        mcdrDetailDT.setStrBusiType(dtOneUserSPlist.getsSpType());
        mcdrDetailDT.setStrSpCode(dtOneUserSPlist.getsSpID());
        mcdrDetailDT.setStrOperCode(dtOneUserSPlist.getsSpBizID());
        mcdrDetailDT.setnChargeFEE((long) marketFee[1]);
        mcdrDetailDT.setMarketId((String) marketFee[0]);
        mcdrDetailDT.setStrDoMainID(dtOneUserSPlist.getsDoMainID());

        mcdrDetailDT.setStrServiceNumber(threadLocalMapList.get("SERVICE_NUMBER"));
        mcdrDetailDT.setStrOrderStartDate(dtOneUserSPlist.getsStartDate());
        mcdrDetailDT.setStrOrderEndDate(dtOneUserSPlist.getsEndDate());
        if (null != threadLocalMapList.get("STATUS")) {
            mcdrDetailDT.setnUSserStatus(Long.parseLong(threadLocalMapList.get("STATUS")));
        }

        mcdrDetailDT.setnErrCode(Long.parseLong(paramBean.getErrorCode()));
        mcdrDetailDT.setStrCreateDate(nowtime);
        mcdrDetailDT.setnMonthNumber(GlobalFunc.get_monthnumber13(Integer.parseInt(String.valueOf(Montnetconfig.changeMonth.get()))));
        mcdrDetailDT.setnChargeType(Montnetconfig.getChargeType());

        if (null == threadLocalMapList.get("SERVICE_NUMBER") || "".equals(threadLocalMapList.get("SERVICE_NUMBER")) || "null".equals(threadLocalMapList.get("SERVICE_NUMBER"))) {
            LogUtil.error("DTL" + "[垃圾数据用户都销户了 还有订购存在 record::" + mcdrDetailDT + "]");
            return;
        }
        threadLocalSettleCdrList.add(mcdrDetailDT);
        return;
    }


    public boolean isUnbillType(CngUserSpData dtOneUserSPlist) {
        //不收费的不生成
        if (dtOneUserSPlist.getnBillFlag() != 0 && dtOneUserSPlist.getnBillFlag() != 1 && dtOneUserSPlist.getnBillFlag() != 4) {
            return false;
        }


        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "FormatSettleCdr---start]");
        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "准备启动，格式话结算文件，dtOneUserSPlist.m_nBillFlag :" + dtOneUserSPlist.getnBillFlag() + "]");

        return true;
    }

    public void formatMBill(CngUserSpData dtOneUserSPlist, List<String> dtOneUserMcdrList, Object[] marketFee, DbResultInfo dbResultInfo,
                            CngExdSpOperDefAllData threadLocaloperInfo, CngExdSpCodeDefAllData threadLocalspInfo, Map<String, String> threadLocalMapList,
                            List<String> threadLocalUnBillLines, List<CngMcdrData> threadLocalMcdrList, ParamBean paramBean,
                            List<CngMcdrDetailData> threadLocalMcdrDetailList) {
        CngMcdrData mcdrDT = new CngMcdrData();

        //用户编号
        mcdrDT.setSzUserID(String.valueOf(dtOneUserSPlist.getsUserID()));
        //CMCC业务代码
        mcdrDT.setSzBusiType(dtOneUserSPlist.getsSpType());
        //企业代码
        mcdrDT.setSzSpCode(dtOneUserSPlist.getsSpID());
        //业务代码
        mcdrDT.setSzOperCode(dtOneUserSPlist.getsSpBizID());
        mcdrDT.setnFee(threadLocaloperInfo.getnFEE());


        if (dtOneUserSPlist.getnBillFlag() == 0 || dtOneUserSPlist.getnBillFlag() == 1 || dtOneUserSPlist.getnBillFlag() == 4) {
            mcdrDT.setnIsCharge(1);
        } else {
            mcdrDT.setnIsCharge(0);
        }

        mcdrDT.setnChargeType(Integer.parseInt(String.valueOf(Montnetconfig.getChargeType())));
        LogUtil.debug(TAG, "threadLocalMcdrList_add userid=  " + dtOneUserSPlist.getsUserID() + "[" + mcdrDT + "]");
        threadLocalMcdrList.add(mcdrDT);

        if (2 == Montnetconfig.getnLoadBillSource()) {
            CngMcdrDetailData mcdrDetailDT = new CngMcdrDetailData();

            Date date = new Date();
            DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
            String nowtime = format.format(date);

            mcdrDetailDT.setStrUserID(String.valueOf(dtOneUserSPlist.getsUserID()));
            mcdrDetailDT.setnBillMonth(Montnetconfig.changeMonth.get());


            if (1 == mcdrDT.getnIsCharge()) {
                LogUtil.debug(TAG, "bbbbbbb1 ");
                //REC_TYPE_CHARGE						= 1;        //收费明细SP
                mcdrDetailDT.setnRecType(1);
            } else {
                LogUtil.debug(TAG, "bbbbbbb2 ");
                //REC_TYPE_UNCHARGE					= 2;        //不收费明细SP
                mcdrDetailDT.setnRecType(2);
            }

            mcdrDetailDT.setStrBusiType(dtOneUserSPlist.getsSpType());
            mcdrDetailDT.setStrSpCode(dtOneUserSPlist.getsSpID());
            mcdrDetailDT.setStrOperCode(dtOneUserSPlist.getsSpBizID());

            mcdrDetailDT.setnChargeFEE((long) marketFee[1]);
            mcdrDetailDT.setMarketId((String) marketFee[0]);

            mcdrDetailDT.setStrDoMainID(dtOneUserSPlist.getsDoMainID());
            mcdrDetailDT.setStrServiceNumber(threadLocalMapList.get("SERVICE_NUMBER"));
            mcdrDetailDT.setStrOrderStartDate(dtOneUserSPlist.getsStartDate());
            mcdrDetailDT.setStrOrderEndDate(dtOneUserSPlist.getsEndDate());
            mcdrDetailDT.setnUSserStatus(Long.parseLong(threadLocalMapList.get("STATUS")));
            mcdrDetailDT.setnErrCode(Long.parseLong(paramBean.getErrorCode()));
            mcdrDetailDT.setStrCreateDate(nowtime);
            mcdrDetailDT.setnMonthNumber(GlobalFunc.get_monthnumber13(Integer.parseInt(String.valueOf(Montnetconfig.changeMonth.get()))));
            mcdrDetailDT.setnChargeType(Montnetconfig.getChargeType());
            threadLocalMcdrDetailList.add(mcdrDetailDT);

            LogUtil.debug(TAG, "threadLocalMcdrDetailList_add userid=  " + dtOneUserSPlist.getsUserID() + "[" + mcdrDetailDT + "]");

        }


        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "user_id:" + mcdrDT.getSzUserID() + "][busi_type:" + mcdrDT.getSzBusiType() + "][sp_code:" +
                mcdrDT.getSzSpCode() + "][oper_code:" + mcdrDT.getSzOperCode() + "][fee:" + mcdrDT.getnFee() + "]");


        StringBuffer str_tmp = new StringBuffer();
        str_tmp.append(mcdrDT.getSzSpCode()).append("|");
        str_tmp.append(mcdrDT.getSzOperCode()).append("|");
        str_tmp.append(mcdrDT.getnIsCharge()).append("|");

        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "add user_id:" + mcdrDT.getSzUserID() + "]");
        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "add data:" + str_tmp.toString() + "]");

        dtOneUserMcdrList.add(str_tmp.toString());
        return;
    }

    public void formatCdr(CngUserSpData dtOneUserSPlist, CngMcdrChargeCfgData mcdrChargeCfgDT, Object[] marketFee, DbResultInfo dbResultInfo,
                          CngExdSpOperDefAllData threadLocaloperInfo, CngExdSpCodeDefAllData threadLocalspInfo, Map<String, String> threadLocalMapList,
                          List<String> threadLocalUnBillLines) {

        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "--start.FormatCdr----]");
        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "开始生成该用户的包月费文件,user_id:" + dtOneUserSPlist.getsUserID() + "]");
        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "mcdrChargeCfgDT->m_strFileName:" + mcdrChargeCfgDT.getsFileName() + "]");

        StringBuffer t_chSeqNum = new StringBuffer();
        StringBuffer cBuf = new StringBuffer();

        StringBuffer m_sFileNo = new StringBuffer();
        StringBuffer m_sAuditInfo = new StringBuffer();

        takeFileNoandAuditInfo(dtOneUserSPlist, m_sFileNo, mcdrChargeCfgDT, m_sAuditInfo, dbResultInfo, threadLocaloperInfo);

        boolean[] isFindCharge = new boolean[1];
        isFindCharge[0] = false;

        buildCmSpCdr(dtOneUserSPlist, cBuf, t_chSeqNum, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildMmsSpCdr(t_chSeqNum, dtOneUserSPlist, cBuf, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildWapSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildFlashSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildStmSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildMobileMapSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildMobileMallSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge, marketFee,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildMobileReadSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildHangDoubleSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildMobileDaohangSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildMobileGameSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildMagicHeSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge, marketFee,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildMiguSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge, marketFee,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildEduHeSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildMusicSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildUnknowSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildLianMingSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildOtherTypeSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildMobileBagSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);
        buildNewGpSpCdr(cBuf, t_chSeqNum, dtOneUserSPlist, m_sFileNo, m_sAuditInfo, isFindCharge, marketFee,
                threadLocaloperInfo, threadLocalspInfo, dbResultInfo, threadLocalMapList, mcdrChargeCfgDT);

        if (!isFindCharge[0]) {
            return;
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtOneUserSPlist.getnBillFlag():" + dtOneUserSPlist.getnBillFlag() + "]");
        }

        if (dtOneUserSPlist.getnBillFlag() == 0 || dtOneUserSPlist.getnBillFlag() == 1 || dtOneUserSPlist.getnBillFlag() == 4) {
            mcdrChargeCfgDT.setsLine(cBuf.toString());
        } else {

            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "cBuf:" + cBuf.toString() + "]");

            threadLocalUnBillLines.add(cBuf.toString());
        }


        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtUnBillLines.size():" + threadLocalUnBillLines.size() + "]");

        return;
    }

    public void buildCmSpCdr(CngUserSpData dtOneUserSPlist, StringBuffer cBuf, StringBuffer t_chSeqNum, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge,
                             CngExdSpOperDefAllData threadLocaloperInfo, CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        if (isCmSpCdrType(threadLocaloperInfo)) {
            String m_sourceType = null;
            String t_callType = null;
            String strStartTime = "235958";

            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00001");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            if ("G".equals(threadLocalspInfo.getStrSpAttr())) //全网                                         SPATTR_DOM  							= "G"; 			//全网SP
            {
                if (threadLocalspInfo.getnProvCode() == 250) {//本省全网业务
                    m_sourceType = "I";
                    t_callType = "02";
                } else //非本省全网业务
                {
                    if ("001104".equals(threadLocaloperInfo.getStrSERVTYPE())) {
                        m_sourceType = "A1";
                        t_callType = "12";
                    }
                    //add by snail for REQ_NGBOSS2014_09139_2  新增12582和工作 090522
                    else if ("090522".equals(threadLocaloperInfo.getStrSERVTYPE()) || "090525".equals(threadLocaloperInfo.getStrSERVTYPE())) {
                        m_sourceType = "r3";
                        t_callType = ""; //经过与计费确认，call_type置为空
                    } else {
                        m_sourceType = "2";
                        t_callType = "12";
                    }
                }
            } else if (threadLocalspInfo.getStrSpAttr().equals("L"))             //SPATTR_PROV 							= "L"; 			//本地SP
            {
                m_sourceType = "I";
                t_callType = "02";
            }

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(String.valueOf(t_chSeqNum)).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append(0).append(COMMA_STR); // 12
            cBuf.append(0).append(COMMA_STR); // 13
            cBuf.append(0).append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("00001").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("").append(COMMA_STR); // 28
            cBuf.append("03").append(COMMA_STR); // 29
            cBuf.append("").append(COMMA_STR); // 30
            cBuf.append("").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 34
            cBuf.append("").append(COMMA_STR); // 35
            cBuf.append("").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("").append(COMMA_STR); // 45
            cBuf.append("").append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public boolean isCmSpCdrType(CngExdSpOperDefAllData threadLocaloperInfo) {
        //CM业务,//分省结算梦网短信,飞信,梦网业务(捐款)
        //add by snail for REQ_NGBOSS2014_09139_2  新增12582和工作 090522
        //add by snail for REQ_NGBOSS2016_0174  和家庭(视频、教育等各类业务) 825001
        //modify by snail for REQ_NGBOSS2016_0174_1 825001改成GP格式，不是梦网MN格式了
        if ("001103".equals(threadLocaloperInfo.getStrSERVTYPE()) || "001102".equals(threadLocaloperInfo.getStrSERVTYPE()) ||
                "001104".equals(threadLocaloperInfo.getStrSERVTYPE()) || "001126".equals(threadLocaloperInfo.getStrSERVTYPE()) ||
                "001122".equals(threadLocaloperInfo.getStrSERVTYPE()) || "090522".equals(threadLocaloperInfo.getStrSERVTYPE()) || "090525".equals(threadLocaloperInfo.getStrSERVTYPE())) {
            return true;
        }
        return false;
    }

    public void buildMmsSpCdr(StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer cBuf, StringBuffer m_sFileNo,
                              StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                              CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        //MMS业务
        if ("000103".equals(threadLocaloperInfo.getStrSERVTYPE())) {
            String m_sourceType = null;
            String t_callType = null;
            String strStartTime = "235958";

            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00001");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            //全网   "G"  //全网SP
            if ("G".equals(threadLocalspInfo.getStrSpAttr())) {
                if (threadLocalspInfo.getnProvCode() == 250) {
                    //本省全网业务
                    m_sourceType = "T";
                    t_callType = "02";
                } else {
                    //非本省全网业务
                    m_sourceType = "X";
                    t_callType = "02";
                }
            } else if ("L".equals(threadLocalspInfo.getStrSpAttr())) {
                m_sourceType = "T";
                t_callType = "02";
            }

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(t_chSeqNum).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("01").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("0").append(COMMA_STR); // 22
            cBuf.append(Montnetconfig.m_strDealTime.get().substring(4, Montnetconfig.m_strDealTime.get().length())).append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("00").append(COMMA_STR); // 28
            cBuf.append("02").append(COMMA_STR); // 29
            cBuf.append("3").append(COMMA_STR); // 30
            cBuf.append("02").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 34
            cBuf.append("").append(COMMA_STR); // 35
            cBuf.append("").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append("").append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("999001").append(COMMA_STR); // 40
            cBuf.append("999001").append(COMMA_STR); // 41
            cBuf.append("0").append(COMMA_STR); // 42
            cBuf.append("0").append(COMMA_STR); // 43
            cBuf.append("00000").append(COMMA_STR); // 44
            cBuf.append("1").append(COMMA_STR); // 45
            cBuf.append("0").append(COMMA_STR); // 46
            cBuf.append("0").append(COMMA_STR); // 47
            cBuf.append("0").append(COMMA_STR); // 48
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public void buildWapSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge
            , CngExdSpOperDefAllData threadLocaloperInfo, CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        //WAP业务
        if (threadLocaloperInfo.getStrSERVTYPE().equals("000104")) {
            String m_sourceType = null;
            String t_callType = null;
            String strStartTime = "235958";
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            char[] strtemp = Montnetconfig.m_strDealTime.get().substring(4, Montnetconfig.m_strDealTime.get().length()).toCharArray();
            strtemp[6] = "0".toCharArray()[0];
            t_chSeqNum.append(String.valueOf(strtemp).substring(0, 6));
            t_chSeqNum.append(String.format("%04d", dbResultInfo.getM_ptSeqnum()));

            //全网    //SPATTR_DOM  							= "G"; 			//全网SP
            if (threadLocalspInfo.getStrSpAttr().equals("G")) {
                if (threadLocalspInfo.getnProvCode() == 250) {
                    //本省全网业务
                    m_sourceType = "0";
                    t_callType = "10";
                } else {
                    //非本省全网业务
                    m_sourceType = "L";
                    t_callType = "10";
                }
            } else if (threadLocalspInfo.getStrSpAttr().equals("L")) {
                //SPATTR_PROV 							= "L"; 			//本地SP
                m_sourceType = "0";
                t_callType = "10";
            }

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(String.valueOf(t_chSeqNum)).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append("").append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("0").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("00002").append(COMMA_STR); // 20
            cBuf.append("999001").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("250").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("02").append(COMMA_STR); // 28
            cBuf.append("03").append(COMMA_STR); // 29
            cBuf.append("0").append(COMMA_STR); // 30
            cBuf.append("4").append(COMMA_STR); // 31
            cBuf.append("00").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 34
            cBuf.append("").append(COMMA_STR); // 35
            cBuf.append("0").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append("").append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("").append(COMMA_STR); // 45
            cBuf.append("").append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            cBuf.append("99"); // 59
            isFindCharge[0] = true;
        }
    }

    public void buildFlashSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                                CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        if (threadLocaloperInfo.getStrSERVTYPE().equals("000105")) //FLASH业务
        {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dbResultInfo.getM_ptSeqnum():" + dbResultInfo.getM_ptSeqnum() + "]");
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "Montnetconfig.m_strDealTime:" + Montnetconfig.m_strDealTime.get() + "]");
            }
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            char[] strtemp = Montnetconfig.m_strDealTime.get().substring(4, Montnetconfig.m_strDealTime.get().length()).toCharArray();
            strtemp[6] = "0".toCharArray()[0];
            t_chSeqNum.append(String.valueOf(strtemp).substring(0, 6));
            t_chSeqNum.append(String.format("%04d", dbResultInfo.getM_ptSeqnum()));

            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_chSeqNum:" + t_chSeqNum + "]");
            }

            m_sourceType = "h";
            t_callType = "10";

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(String.valueOf(t_chSeqNum)).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append("").append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("0").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("00002").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("250").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("02").append(COMMA_STR); // 28
            cBuf.append("03").append(COMMA_STR); // 29
            cBuf.append("").append(COMMA_STR); // 30
            cBuf.append("4").append(COMMA_STR); // 31
            cBuf.append("00").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 34
            cBuf.append("0").append(COMMA_STR); // 35
            cBuf.append("0").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append("").append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("99").append(COMMA_STR); // 45
            cBuf.append("").append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public void buildStmSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                              CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";

        //STM业务
        if (threadLocaloperInfo.getStrSERVTYPE().equals("001123")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().substring(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00004");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "i";
            t_callType = "20";

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(String.valueOf(t_chSeqNum)).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append("").append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("1").append(COMMA_STR); // 12
            cBuf.append("03").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append("03").append(COMMA_STR); // 16
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("0").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("1202101").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("1202101").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("250").append(COMMA_STR); // 25
            cBuf.append("250").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("").append(COMMA_STR); // 28
            cBuf.append("03").append(COMMA_STR); // 29
            cBuf.append("").append(COMMA_STR); // 30
            cBuf.append("").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("1").append(COMMA_STR); // 33
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 34
            cBuf.append("0").append(COMMA_STR); // 35
            cBuf.append("").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append("000000000000000000").append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("00").append(COMMA_STR); // 44
            cBuf.append("").append(COMMA_STR); // 45
            cBuf.append("5").append(COMMA_STR); // 46
            cBuf.append("3").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("000000000000000").append(COMMA_STR); // 49
            cBuf.append("00").append(COMMA_STR); // 50
            cBuf.append("000000000000000").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("0").append(COMMA_STR); // 53
            cBuf.append("0").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public void buildMobileMapSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                                    CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";

        //手机地图
        if (threadLocaloperInfo.getStrSERVTYPE().equals("008002")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().substring(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00007");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "q";
            t_callType = "20";

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(String.valueOf(t_chSeqNum)).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append("").append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("0").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("").append(COMMA_STR); // 28
            cBuf.append("3").append(COMMA_STR); // 29
            cBuf.append("").append(COMMA_STR); // 30
            cBuf.append("").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 34
            cBuf.append("0").append(COMMA_STR); // 35
            cBuf.append("").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append("").append(COMMA_STR); // 38
            cBuf.append("1").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("1").append(COMMA_STR); // 45
            cBuf.append("").append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("0").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public void buildMobileMallSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, Object[] marketFee, CngExdSpOperDefAllData threadLocaloperInfo,
                                     CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        //移动商城
        if (threadLocaloperInfo.getStrSERVTYPE().equals("008011")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().substring(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00009");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "A8";
            t_callType = "";

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(String.valueOf(t_chSeqNum)).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("00009").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("0").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("").append(COMMA_STR); // 28
            cBuf.append("03").append(COMMA_STR); // 29
            cBuf.append("01").append(COMMA_STR); // 30
            cBuf.append("0").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append(marketFee[1]).append(COMMA_STR); // 34
            cBuf.append("0").append(COMMA_STR); // 35
            cBuf.append("0").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append("").append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("00").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("").append(COMMA_STR); // 45
            cBuf.append(marketFee[0]).append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public void buildMobileReadSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                                     CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        //手机阅读
        if (threadLocaloperInfo.getStrSERVTYPE().equals("090508")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().substring(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "B2";
            t_callType = "";

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(String.valueOf(t_chSeqNum)).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append("").append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("0").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("01").append(COMMA_STR); // 28
            cBuf.append("03").append(COMMA_STR); // 29
            cBuf.append("").append(COMMA_STR); // 30
            cBuf.append("0").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 34
            cBuf.append("0").append(COMMA_STR); // 35
            cBuf.append("0").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("03").append(COMMA_STR); // 45
            cBuf.append("").append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("0").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public void buildHangDoubleSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                                     CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        //杭研双创
        if (threadLocaloperInfo.getStrSERVTYPE().equals("009001")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);

            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().substring(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            if (threadLocalspInfo.getStrSpAttr().equals("G")) //全网
            {
                if (threadLocalspInfo.getnProvCode() == 250) {
                    //本省全网业务
                    m_sourceType = "R1";
                    t_callType = "";
                } else {
                    //非本省全网业务
                    m_sourceType = "R1";
                    t_callType = "10";
                }
            } else if (threadLocalspInfo.getStrSpAttr().equals("L")) {
                m_sourceType = "R1";
                t_callType = "";
            }

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(String.valueOf(t_chSeqNum)).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append("").append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("0").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("01").append(COMMA_STR); // 28
            cBuf.append("03").append(COMMA_STR); // 29
            cBuf.append("").append(COMMA_STR); // 30
            cBuf.append("0").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 34
            cBuf.append("0").append(COMMA_STR); // 35
            cBuf.append("0").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("03").append(COMMA_STR); // 45
            cBuf.append("").append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("0").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public void buildMobileDaohangSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                                        CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        //手机导航网络版
        if (threadLocaloperInfo.getStrSERVTYPE().equals("008003")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);

            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().substring(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "B6";
            t_callType = "";

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(String.valueOf(t_chSeqNum)).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append("").append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("0").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append("01").append(COMMA_STR); // 17
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("").append(COMMA_STR); // 28
            cBuf.append("03").append(COMMA_STR); // 29
            cBuf.append("").append(COMMA_STR); // 30
            cBuf.append("").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("0").append(COMMA_STR); // 33
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 34
            cBuf.append("0").append(COMMA_STR); // 35
            cBuf.append("0").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("03").append(COMMA_STR); // 45
            cBuf.append("").append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("0").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public void buildMobileGameSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                                     CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        if ("008007".equals(threadLocaloperInfo.getStrSERVTYPE()) || "825001".equals(threadLocaloperInfo.getStrSERVTYPE()) || "009106".equals(threadLocaloperInfo.getStrSERVTYPE())) //手机游戏
        {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            long longAdd = 0;
            if ("825001".equals(threadLocaloperInfo.getStrSERVTYPE()) || "009106".equals(threadLocaloperInfo.getStrSERVTYPE())) //和家庭(视频、教育等各类业务)改成r9
            {
                m_sourceType = "r9";
                longAdd = threadLocaloperInfo.getnFEE();
            } else {
                m_sourceType = "r";
            }
            t_callType = "02";

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(t_chSeqNum.toString()).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append("").append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("0").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("01").append(COMMA_STR); // 28
            cBuf.append("04").append(COMMA_STR); // 29
            cBuf.append("").append(COMMA_STR); // 30
            cBuf.append("0").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 34
            cBuf.append(longAdd).append(COMMA_STR); // 35
            cBuf.append("0").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("03").append(COMMA_STR); // 45
            cBuf.append("").append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("0").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public void buildMagicHeSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, Object[] marketFee, CngExdSpOperDefAllData threadLocaloperInfo,
                                  CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        if (threadLocaloperInfo.getStrSERVTYPE().equals("090532")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "t0";
            t_callType = "02";

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(t_chSeqNum.toString()).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append("").append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("0").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("01").append(COMMA_STR); // 28
            cBuf.append("04").append(COMMA_STR); // 29
            cBuf.append("").append(COMMA_STR); // 30
            cBuf.append("0").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append(marketFee[1]).append(COMMA_STR); // 34
            cBuf.append(marketFee[1]).append(COMMA_STR); // 35
            cBuf.append("0").append(COMMA_STR); // 36
            cBuf.append(dtOneUserSPlist.getsProdInstanceID()).append(COMMA_STR); // 37
            cBuf.append(threadLocalMapList.get("")).append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("03").append(COMMA_STR); // 45
            cBuf.append(marketFee[0]).append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("0").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public void buildMiguSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, Object[] marketFee, CngExdSpOperDefAllData threadLocaloperInfo,
                               CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        if (threadLocaloperInfo.getStrSERVTYPE().equals("090526")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);

            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "r8";
            t_callType = "02";

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(t_chSeqNum.toString()).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append("").append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("0").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("01").append(COMMA_STR); // 28
            cBuf.append("04").append(COMMA_STR); // 29
            cBuf.append("").append(COMMA_STR); // 30
            cBuf.append("0").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append(marketFee[1]).append(COMMA_STR); // 34
            cBuf.append(marketFee[1]).append(COMMA_STR); // 35
            cBuf.append("0").append(COMMA_STR); // 36
            cBuf.append(dtOneUserSPlist.getsProdInstanceID()).append(COMMA_STR); // 37
            cBuf.append(threadLocalMapList.get("")).append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("03").append(COMMA_STR); // 45
            cBuf.append(marketFee[0]).append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("0").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public void buildEduHeSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                                CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        if (threadLocaloperInfo.getStrSERVTYPE().equals("090527")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);

            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "r5";
            LogUtil.debug(LogProperty.LOGTYPE_SYS, TAG + "serv_type=" + "090527");
            t_callType = "02";

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(t_chSeqNum.toString()).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append("").append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("0").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("01").append(COMMA_STR); // 28
            cBuf.append("04").append(COMMA_STR); // 29
            cBuf.append("").append(COMMA_STR); // 30
            cBuf.append("0").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append("0").append(COMMA_STR); // 34
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 35
            cBuf.append("0").append(COMMA_STR); // 36
            cBuf.append(dtOneUserSPlist.getsProdInstanceID()).append(COMMA_STR); // 37
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("03").append(COMMA_STR); // 45
            cBuf.append("").append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("0").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public void buildMusicSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                                CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        if (threadLocaloperInfo.getStrSERVTYPE().equals("008001")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "k";
            t_callType = "";

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(t_chSeqNum.toString()).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append("").append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("0").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("01").append(COMMA_STR); // 28
            cBuf.append("03").append(COMMA_STR); // 29
            cBuf.append("").append(COMMA_STR); // 30
            cBuf.append("0").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 34
            cBuf.append("0").append(COMMA_STR); // 35
            cBuf.append("0").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("03").append(COMMA_STR); // 45
            cBuf.append("").append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("0").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            cBuf.append("0"); // 59
            isFindCharge[0] = true;
        }
    }

    public void buildUnknowSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                                 CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        if (threadLocaloperInfo.getStrSERVTYPE().equals("000204")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "6";
            t_callType = "";

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(t_chSeqNum.toString()).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append("").append(COMMA_STR); // 10
            cBuf.append("").append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("").append(COMMA_STR); // 13
            cBuf.append("").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append("2").append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append(dtOneUserSPlist.getsPriorOrderDate()).append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("").append(COMMA_STR); // 28
            cBuf.append("1").append(COMMA_STR); // 29
            cBuf.append("6").append(COMMA_STR); // 30
            cBuf.append("1").append(COMMA_STR); // 31
            cBuf.append("1").append(COMMA_STR); // 32
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 33
            cBuf.append("").append(COMMA_STR); // 34
            cBuf.append("").append(COMMA_STR); // 35
            cBuf.append("").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append("").append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("").append(COMMA_STR); // 45
            cBuf.append("").append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("").append(COMMA_STR); // 51
            cBuf.append("").append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            cBuf.append(dtOneUserSPlist.getsSpBizID()); // 59
            isFindCharge[0] = true;
        }
    }

    public void buildLianMingSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                                   CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";

        if (threadLocaloperInfo.getStrSERVTYPE().equals("910566")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);

            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "r7";
            t_callType = "02";

            cBuf.append(m_sourceType).append(COMMA_STR); // 0
            cBuf.append(t_callType).append(COMMA_STR); // 1
            cBuf.append(t_chSeqNum.toString()).append(COMMA_STR); // 2
            cBuf.append("").append(COMMA_STR); // 3
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR); // 4
            cBuf.append("").append(COMMA_STR); // 5
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 6
            cBuf.append("").append(COMMA_STR); // 7
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 8
            cBuf.append(strStartTime).append(COMMA_STR); // 9
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR); // 10
            cBuf.append(strStartTime).append(COMMA_STR); // 11
            cBuf.append("0").append(COMMA_STR); // 12
            cBuf.append("0").append(COMMA_STR); // 13
            cBuf.append("0").append(COMMA_STR); // 14
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR); // 15
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR); // 16
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR); // 17
            cBuf.append("").append(COMMA_STR); // 18
            cBuf.append("").append(COMMA_STR); // 19
            cBuf.append("").append(COMMA_STR); // 20
            cBuf.append("").append(COMMA_STR); // 21
            cBuf.append("").append(COMMA_STR); // 22
            cBuf.append("").append(COMMA_STR); // 23
            cBuf.append("250").append(COMMA_STR); // 24
            cBuf.append("").append(COMMA_STR); // 25
            cBuf.append("").append(COMMA_STR); // 26
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT); // 27
            cBuf.append("01").append(COMMA_STR); // 28
            cBuf.append("04").append(COMMA_STR); // 29
            cBuf.append("").append(COMMA_STR); // 30
            cBuf.append("0").append(COMMA_STR); // 31
            cBuf.append("0").append(COMMA_STR); // 32
            cBuf.append("").append(COMMA_STR); // 33
            cBuf.append("").append(COMMA_STR); // 34
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR); // 35
            cBuf.append("0").append(COMMA_STR); // 36
            cBuf.append("").append(COMMA_STR); // 37
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR); // 38
            cBuf.append("").append(COMMA_STR); // 39
            cBuf.append("").append(COMMA_STR); // 40
            cBuf.append("").append(COMMA_STR); // 41
            cBuf.append("").append(COMMA_STR); // 42
            cBuf.append("").append(COMMA_STR); // 43
            cBuf.append("").append(COMMA_STR); // 44
            cBuf.append("03").append(COMMA_STR); // 45
            cBuf.append("").append(COMMA_STR); // 46
            cBuf.append("").append(COMMA_STR); // 47
            cBuf.append("").append(COMMA_STR); // 48
            cBuf.append("").append(COMMA_STR); // 49
            cBuf.append("").append(COMMA_STR); // 50
            cBuf.append("0").append(COMMA_STR); // 51
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR); // 52
            cBuf.append("").append(COMMA_STR); // 53
            cBuf.append("").append(COMMA_STR); // 54
            cBuf.append(m_sFileNo).append(COMMA_STR); // 55
            cBuf.append("000").append(COMMA_STR); // 56
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR); // 57
            cBuf.append(m_sAuditInfo).append(COMMA_STR); // 58
            isFindCharge[0] = true;
        }
    }

    public void buildOtherTypeSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                                    CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        if (threadLocaloperInfo.getStrSERVTYPE().equals("090516")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);
            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().substring(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "b1";
            t_callType = "03";

            cBuf.append(m_sourceType).append(COMMA_STR);
            cBuf.append(t_callType).append(COMMA_STR);
            cBuf.append(t_chSeqNum.toString()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR);
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("250").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT);
            cBuf.append("1").append(COMMA_STR);
            cBuf.append("03").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(m_sFileNo).append(COMMA_STR);
            cBuf.append("000").append(COMMA_STR);
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR);
            cBuf.append(m_sAuditInfo).append(COMMA_STR);
            isFindCharge[0] = true;
        }
    }

    public void buildMobileBagSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, CngExdSpOperDefAllData threadLocaloperInfo,
                                    CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        //手机钱包
        if (threadLocaloperInfo.getStrSERVTYPE().equals("090521")) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);

            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "B9";
            t_callType = "02";

            cBuf.append(m_sourceType).append(COMMA_STR);
            cBuf.append(t_callType).append(COMMA_STR);
            cBuf.append(t_chSeqNum.toString()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR);
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("250").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT);
            cBuf.append("01").append(COMMA_STR);
            cBuf.append("04").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("03").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(m_sFileNo).append(COMMA_STR);
            cBuf.append("000").append(COMMA_STR);
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR);
            cBuf.append(m_sAuditInfo).append(COMMA_STR);
            isFindCharge[0] = true;
        }
    }

    public void buildNewGpSpCdr(StringBuffer cBuf, StringBuffer t_chSeqNum, CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, StringBuffer m_sAuditInfo, boolean[] isFindCharge, Object[] marketFee, CngExdSpOperDefAllData threadLocaloperInfo,
                                CngExdSpCodeDefAllData threadLocalspInfo, DbResultInfo dbResultInfo, Map<String, String> threadLocalMapList, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        String m_sourceType = null;
        String t_callType = null;
        String strStartTime = "235958";
        if ("008013".equals(threadLocaloperInfo.getStrSERVTYPE())) {
            dbResultInfo.setM_ptSeqnum((dbResultInfo.getM_ptSeqnum() + 1) % 100000);

            t_chSeqNum.append(Montnetconfig.m_strDealTime.get().subSequence(4, Montnetconfig.m_strDealTime.get().length()));
            t_chSeqNum.append("00010");
            t_chSeqNum.append(String.format("%05d", dbResultInfo.getM_ptSeqnum()));

            m_sourceType = "r6";
            t_callType = "02";

            cBuf.append(m_sourceType).append(COMMA_STR);
            cBuf.append(t_callType).append(COMMA_STR);
            cBuf.append(t_chSeqNum.toString()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR);
            cBuf.append(strStartTime).append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR);
            cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("250").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT);
            cBuf.append("01").append(COMMA_STR);
            cBuf.append("03").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(marketFee[1]).append(COMMA_STR);
            cBuf.append(marketFee[1]).append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("03").append(COMMA_STR);
            cBuf.append(marketFee[0]).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("0").append(COMMA_STR);
            cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append("").append(COMMA_STR);
            cBuf.append(m_sFileNo).append(COMMA_STR);
            cBuf.append("000").append(COMMA_STR);
            cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR);
            cBuf.append(m_sAuditInfo).append(COMMA_STR);
            isFindCharge[0] = true;
        }
    }

    private void appendInstanceId(StringBuffer cBuf, CngUserSpData dtOneUserSPlist, CngMcdrChargeCfgData mcdrChargeCfgDT) {
        if (Montnetconfig.getChargeType() == 1 && "GP".equals(mcdrChargeCfgDT.getsFILENAMEPRIX())) {
            cBuf.append(dtOneUserSPlist.getInstanceID()).append(COMMA_STR);
        } else {
            cBuf.append("").append(COMMA_STR);
        }
    }

    public void takeFileNoandAuditInfo(CngUserSpData dtOneUserSPlist, StringBuffer m_sFileNo, CngMcdrChargeCfgData mcdrChargeCfgDT,
                                       StringBuffer m_sAuditInfo, DbResultInfo dbResultInfo, CngExdSpOperDefAllData threadLocaloperInfo) {
        if (dtOneUserSPlist.getnBillFlag() == 0 || dtOneUserSPlist.getnBillFlag() == 1 || dtOneUserSPlist.getnBillFlag() == 4) {
            m_sFileNo.append("3");
            m_sFileNo.append(mcdrChargeCfgDT.getsFileName().substring(1, 14));
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "mcdrChargeCfgDT.getsFileName():" + mcdrChargeCfgDT.getsFileName() + "]");
            }
            m_sFileNo.append(mcdrChargeCfgDT.getsFileName().substring(24, 29));
        } else {
            m_sFileNo.append(dbResultInfo.getStrUnBillFileName().substring(0, 14));
            m_sFileNo.append(dbResultInfo.getStrUnBillFileName().substring(24, 29));
        }

        m_sAuditInfo.append('0');
        m_sAuditInfo.append(Montnetconfig.m_strDealTime.get());
        m_sAuditInfo.append(m_sFileNo);

        //不收费文件重写 m_sAuditInfo  m_sFileNo
        if (dtOneUserSPlist.getnBillFlag() != 0 && dtOneUserSPlist.getnBillFlag() != 1 && dtOneUserSPlist.getnBillFlag() != 4) {
            m_sFileNo.delete(0, m_sFileNo.length());
            m_sFileNo.append("m_sFileNo");
            m_sAuditInfo.delete(0, m_sAuditInfo.length());
            m_sAuditInfo.append("m_sAuditInfo");
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "m_sAuditInfo:" + m_sAuditInfo + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "operInfo.m_strSERV_TYPE:" + threadLocaloperInfo.getStrSERVTYPE() + "]");
        }
    }

    /**
     * @return java.lang.Object[]
     * @Description 营销折扣活动
     * @Date 15:27 2021/6/8
     * @Param [userSpData]
     */
    public Object[] parseMarketFee(CngUserSpData userSpData, CngExdSpOperDefAllData threadLocaloperInfo, DbResultInfo dbResultInfo) {
        //根据user_sp表中的“营销活动编号MARKET_ID”读取营销活动局数据中的“结算优惠起始账期BEGIN_PERIOD”字段和“结算优惠时长BAL_MONTH”字段，计算优惠时长
        //例如起始账期为4，时长为3，表示从订购第四个账期月开始优惠3个账期月。
        Object[] objects = new Object[2];
        objects[0] = "";
        objects[1] = threadLocaloperInfo.getnFEE();

        List<CngExdMiguCampData> exdList = dbResultInfo.getMapMiguCampInfo().get(userSpData.getMarketId());
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "根据getMarketId :" + userSpData.getMarketId() + " 找到的mapMiguCampInfo ：" + exdList + "]");
        }
        if (null != exdList && !exdList.isEmpty()) {
            LocalDateTime orderDate = LocalDateTime.parse(userSpData.getsStartDate(), Montnetconfig.dateTimeFormatter);
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime beginDate = orderDate.plusMonths(Integer.parseInt(exdList.get(0).getBeginPeriod()) - 1).with(TemporalAdjusters.firstDayOfMonth());
            LocalDateTime endDate = orderDate.plusMonths(Integer.parseInt(exdList.get(0).getBeginPeriod()) + Integer.parseInt(exdList.get(0).getBalMonth()) - 2).with(TemporalAdjusters.lastDayOfMonth());

            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "营销活动beginDate :" + beginDate + "  endDate ：" + endDate + "]");
            }

            if (now.isAfter(beginDate) && now.isBefore(endDate)) {
                objects[0] = exdList.get(0).getCampaignId();
                objects[1] = Long.valueOf(exdList.get(0).getBalPrice());
                return objects;
            }

        }

        return objects;
    }

    //判断是否已经生成了互斥业务包月话单
    public boolean isCreatedConflictMcdr(List<CngUserSpData> dtOneUserSPlist, List<String> dtOneUserMcdrList, DbResultInfo dbResultInfo) {

        int nIsCharge = -1;

        nIsCharge = getIsCharge(dtOneUserSPlist);

        StringBuffer strSourOper = new StringBuffer();
        strSourOper.append(dtOneUserSPlist.get(0).getsSpID());
        strSourOper.append("~");
        strSourOper.append(dtOneUserSPlist.get(0).getsSpBizID());

        List<String> dtConfList = new ArrayList<>();

        //查找互斥业务
        if (dbResultInfo.getMapConflictOper().get(strSourOper.toString()) == null) {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "conflict oper NOT FOUND]");
            }
            return false;
        }
        getConfList(dtConfList, strSourOper, dbResultInfo);

        for (int i = 0; i < dtConfList.size(); i++) {
            if (isFindConfig(dtOneUserSPlist, nIsCharge, dtConfList, i, dbResultInfo)) {
                return true;
            }
            if (isAlreadyCharge(dtOneUserMcdrList, i, dtConfList, nIsCharge, dtOneUserSPlist)) {
                return true;
            }
        }

        return false;
    }

    public boolean isAlreadyCharge(List<String> dtOneUserMcdrList, int i, List<String> dtConfList, int nIsCharge, List<CngUserSpData> dtOneUserSPlist) {
        for (int www = 0; www < dtOneUserMcdrList.size(); www++) {
            String[] cdrStr = dtOneUserMcdrList.get(www).split("\\|");
            StringBuffer strConfOper_1 = new StringBuffer();
            strConfOper_1.append(cdrStr[0]).append("~");
            strConfOper_1.append(cdrStr[1]);

            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "内存中找到该用户的收费记录data:" + strConfOper_1.toString() + "]");
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "配置中的data:" + dtConfList.get(i) + "]");
            }

            if (dtConfList.get(i).equals(strConfOper_1.toString()) && nIsCharge == Integer.parseInt(cdrStr[2])) {
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "该业务的互斥业务收过了,不需要再收取,user_id:" + dtOneUserSPlist.get(0).getsUserID() +
                            "][sp_id:" + cdrStr[0] + "][oper_code:" + cdrStr[1] + "][nIsCharge:" + nIsCharge + "][mapMcdrList.m_nIsCharge:" + cdrStr[2] + "]");
                }
                return true;
            }
        }
        return false;
    }

    public boolean isFindConfig(List<CngUserSpData> dtOneUserSPlist, int nIsCharge, List<String> dtConfList, int i, DbResultInfo dbResultInfo) {
        if (dbResultInfo.getMapMcdrList().containsKey(String.valueOf(dtOneUserSPlist.get(0).getsUserID()))) {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "找到该用户内存中的收费记录:" + dtOneUserSPlist.get(0).getsUserID() + "]");
            }
            for (int col = 0; col < dbResultInfo.getMapMcdrList().get(String.valueOf(dtOneUserSPlist.get(0).getsUserID())).size(); col++) {
                StringBuffer strConfOper_1 = new StringBuffer();
                strConfOper_1.append(dbResultInfo.getMapMcdrList().get(String.valueOf(dtOneUserSPlist.get(0).getsUserID())).get(col).getSzSpCode());
                strConfOper_1.append("~");
                strConfOper_1.append(dbResultInfo.getMapMcdrList().get(String.valueOf(dtOneUserSPlist.get(0).getsUserID())).get(col).getSzOperCode());

                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "找到该用户内存中的收费记录strConfOper_1=:" + strConfOper_1.toString() + "]");
                }

                if (dtConfList.get(i).equals(strConfOper_1.toString()) && nIsCharge == dbResultInfo.getMapMcdrList().get(String.valueOf(dtOneUserSPlist.get(0).getsUserID())).get(col).getnIsCharge()) {
                    if (LogUtil.isDebugEnable()) {
                        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "该业务的互斥业务收过了,不需要再收取,user_id:" + dtOneUserSPlist.get(0).getsUserID() +
                                "][sp_id:" + dbResultInfo.getMapMcdrList().get(String.valueOf(dtOneUserSPlist.get(0).getsUserID())).get(col).getSzSpCode() + "]{oper_code:" +
                                dbResultInfo.getMapMcdrList().get(String.valueOf(dtOneUserSPlist.get(0).getsUserID())).get(col).getSzOperCode() + "][nIsCharge:" + nIsCharge
                                + "][pmapMcdrList.m_nIsCharge:" + dbResultInfo.getMapMcdrList().get(String.valueOf(dtOneUserSPlist.get(0).getsUserID())).get(col).getnIsCharge() + "]");
                    }
                    return true;
                }
            }
        }
        return false;
    }

    public void getConfList(List<String> dtConfList, StringBuffer strSourOper, DbResultInfo dbResultInfo) {
        for (int row = 0; row < dbResultInfo.getMapConflictOper().get(strSourOper.toString()).size(); row++) {
            String strConfOper = "";
            strConfOper = dbResultInfo.getMapConflictOper().get(strSourOper.toString()).get(row);
            dtConfList.add(strConfOper);
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "strConfOper:" + strConfOper + "]");
            }
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtConfList.size:" + dtConfList.size() + "]");
        }
    }

    public int getIsCharge(List<CngUserSpData> dtOneUserSPlist) {
        if (dtOneUserSPlist.get(0).getnBillFlag() == 0 || dtOneUserSPlist.get(0).getnBillFlag() == 1 || dtOneUserSPlist.get(0).getnBillFlag() == 4) {
            return 1;
        } else {
            return 0;
        }
    }

    public void reSetErrorCode(List<CngUserSpData> dtUserSPlistONE, List<String> dtOneUserMcdrList, ParamBean paramBean, DbResultInfo dbResultInfo) {

        if ("0".equals(paramBean.getErrorCode()) && isCreatedConflictMcdr(dtUserSPlistONE, dtOneUserMcdrList, dbResultInfo)) {
            //MCDR_ERROR_OPER_CONFLICT  	= 203; 			//业务互斥，同时开两个业务只收一个业务的钱
            paramBean.setErrorCode("203");
        }
    }

    public void processErrorCode(CngUserSpData t_orderInfo, CngMcdrChargeCfgData mcdrChargeCfgDT, DbResultInfo dbResultInfo,
                                 ParamBean paramBean, CngExdSpOperDefAllData threadLocaloperInfo, Map<String, String> threadLocalMapList) {
        {
            StringBuffer nFlag = new StringBuffer();
            //判断是否SP处罚业务
            if (isPunishSpServ(t_orderInfo, dbResultInfo)) {
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "punish sp oper,sp_id:" + t_orderInfo.getsSpID() + "][biz_id:" + t_orderInfo.getsSpBizID() + "]");
                }
                paramBean.setErrorCode("300");
            }

            //判断用户状态是否正常
            if (!isUserStatusOK(t_orderInfo, threadLocalMapList)) {
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "该用户状态不正常]");
                }
                paramBean.setErrorCode("100");
            }

            //判断是否单高或沉默用户
            if (isSilenceOrHighCdr(t_orderInfo, nFlag, mcdrChargeCfgDT, dbResultInfo, threadLocaloperInfo)) {
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----该用户的该业务是单高或沉默业务]");
                }

                int m_nFlag = Integer.parseInt(nFlag.toString());
                switch (m_nFlag) {
                    case 1:
                        //MCDR_ERROR_SILEN       			= 201; 			//沉默
                        paramBean.setErrorCode("201");
                        break;
                    case 2:
                        //MCDR_ERROR_SILHIGH     			= 202; 			//单高
                        paramBean.setErrorCode("202");
                        break;
                    default:
                        //MCDR_ERROR_HIGH							= 200; 			//单高沉默
                        paramBean.setErrorCode("200");
                        break;
                }
            }
        }
    }

    public boolean isSilenceOrHighCdr(CngUserSpData dtOneUserSPlist, StringBuffer r_flag, CngMcdrChargeCfgData mcdrChargeCfgDT, DbResultInfo dbResultInfo, CngExdSpOperDefAllData threadLocaloperInfo) {
        //设置初始状态
        long m_nStatus = -1;
        m_nStatus = mcdrChargeCfgDT.getsSingularStatus();
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start-isSilenceOrHighCdr]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----dtOneUserSPlist.getsSpID():" + dtOneUserSPlist.getsSpID() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----dtOneUserSPlist.getsSpBizID():" + dtOneUserSPlist.getsSpBizID() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "单高沉默,m_nStatus:" + m_nStatus + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "cngSpSingularStateDT.mapSpSingularList.size():" + dbResultInfo.mapSpSingularList.size() + "]");
        }

        //查询话单条数
        long nQryCdrLimit = 0;
        long[] qryCdrLimit = new long[2];

        if (getQryCdrLimit(qryCdrLimit, dtOneUserSPlist, dbResultInfo)) {
            m_nStatus = qryCdrLimit[0];
            nQryCdrLimit = qryCdrLimit[1];
        }

        switch (Integer.parseInt(String.valueOf(m_nStatus))) {
            //不判断单高沉默
            case 0:
                return false;

            //判断沉默
            case 1:
                return checkIsSilence(dtOneUserSPlist, nQryCdrLimit, r_flag, dbResultInfo);
            //判断单高沉默
            case 2:
                return checkSilenceAndHighCdr(r_flag, dtOneUserSPlist, nQryCdrLimit, dbResultInfo, threadLocaloperInfo);
            default:
                return false;
        }
    }

    public boolean checkIsSilence(CngUserSpData dtOneUserSPlist, long nQryCdrLimit, StringBuffer r_flag, DbResultInfo dbResultInfo) {
        long nQryCdrNum = 0;
        StringBuffer nCdrNum = new StringBuffer();
        if (getUserMCFcdrInfo(dtOneUserSPlist, nCdrNum, dbResultInfo)) {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "--nCdrNum--参数能取出值来么:" + nCdrNum + "]");
            }
            nQryCdrNum = Long.parseLong(nCdrNum.toString());
            if (nQryCdrLimit != 0 && nQryCdrNum < nQryCdrLimit) {
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "查询话单条数不够,nQryCdrNum:" + nQryCdrNum + "][nQryCdrLimit:" + nQryCdrLimit + "]");
                }
                r_flag.append(2);
                return true;
            }

            if (nQryCdrNum > 0) {
                return false;
            }
        }

        r_flag.append(1);
        return true;
    }


    public boolean checkSilenceAndHighCdr(StringBuffer r_flag, CngUserSpData dtOneUserSPlist, long nQryCdrLimit, DbResultInfo dbResultInfo, CngExdSpOperDefAllData threadLocaloperInfo) {
        long nQryCdrNum = 0;
        StringBuffer nCdrNum2 = new StringBuffer();
        if (getUserMCFcdrInfo(dtOneUserSPlist, nCdrNum2, dbResultInfo)) {
            nQryCdrNum = Long.parseLong(nCdrNum2.toString());
            if (nQryCdrLimit != 0 && nQryCdrNum < nQryCdrLimit) {
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "查询话单条数不够,nQryCdrNum:" + nQryCdrNum + "][nQryCdrLimit:" + nQryCdrLimit + "]");
                }
                r_flag.append(2);
                return true;
            }

            //根据费用和查询话单条数判断是否单高用户
            //单条过高
            if (nQryCdrNum > 0 && threadLocaloperInfo.getnFEE() <= nQryCdrNum * 2000)            //CM话单单条过高费用上限
            {
                return false;
            } else {
                //平均每条话单费用大于HIGH_CDR_FEERATE_CM为单高
                r_flag.append(2);
                return true;
            }
        }
        r_flag.append(1);
        return true;
    }

    public boolean getUserMCFcdrInfo(CngUserSpData dtOneUserSPlist, StringBuffer nCdrNum, DbResultInfo dbResultInfo) {
        if (LogUtil.isDebugEnable()) {

            if (CollUtil.isNotEmpty(dbResultInfo.getMapQryCdrList())) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "cngSpMonthQryCdrStatDT.mapQryCdrList.size():" + dbResultInfo.getMapQryCdrList() + "]");

            }
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "cngSpMonthQryCdrStatDT.mapQryCdrList.size():" + dbResultInfo.getMapQryCdrList().size() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtOneUserSPlist.getsUserIDDone():" + dtOneUserSPlist.getsUserIDDone() + "]");
        }

        if (dbResultInfo.getMapQryCdrList().containsKey(dtOneUserSPlist.getsUserIDDone())) {
            for (int row = 0; row < dbResultInfo.getMapQryCdrList().get(dtOneUserSPlist.getsUserIDDone()).size(); row++) {
                if (dbResultInfo.getMapQryCdrList().get(dtOneUserSPlist.getsUserIDDone()).get(row).getSzSPID().equals(dtOneUserSPlist.getsSpID()) &&
                        dbResultInfo.getMapQryCdrList().get(dtOneUserSPlist.getsUserIDDone()).get(row).getSzSPBIZID().equals(dtOneUserSPlist.getsSpBizID())) {
                    nCdrNum.append(dbResultInfo.getMapQryCdrList().get(dtOneUserSPlist.getsUserIDDone()).get(row).getSzCDRNUM());
                    if (LogUtil.isDebugEnable()) {
                        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "user_id:" + dtOneUserSPlist.getsUserID() + "][done_user_id:" + dtOneUserSPlist.getsUserIDDone()
                                + "][sp_id:" + dtOneUserSPlist.getsSpID() + "][biz_id:" + dtOneUserSPlist.getsSpBizID() + "][nCdrNum:" + nCdrNum.toString() + "]");
                    }
                    return true;
                }
            }
        }

        return false;
    }

    public boolean getQryCdrLimit(long[] qryCdrLimit, CngUserSpData dtOneUserSPlist, DbResultInfo dbResultInfo) {
        if (dbResultInfo.getMapSpSingularList().containsKey(dtOneUserSPlist.getsSpID())) {
            for (int row = 0; row < dbResultInfo.getMapSpSingularList().get(dtOneUserSPlist.getsSpID()).size(); row++) {
                if (dbResultInfo.getMapSpSingularList().get(dtOneUserSPlist.getsSpID()).get(row).getStrSPID().equals(dtOneUserSPlist.getsSpID()) &&
                        (dbResultInfo.getMapSpSingularList().get(dtOneUserSPlist.getsSpID()).get(row).getStrSPBIZID().equals(dtOneUserSPlist.getsSpBizID()) ||
                                dbResultInfo.getMapSpSingularList().get(dtOneUserSPlist.getsSpID()).get(row).getStrSPBIZID().equals("*"))) {
                    qryCdrLimit[0] = dbResultInfo.getMapSpSingularList().get(dtOneUserSPlist.getsSpID()).get(row).getnSTATUS();
                    qryCdrLimit[1] = dbResultInfo.getMapSpSingularList().get(dtOneUserSPlist.getsSpID()).get(row).getnQRYCDRNUM();
                    if (LogUtil.isDebugEnable()) {
                        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "单高沉默,m_nStatus:" + qryCdrLimit[0] + "][nQryCdrLimit:" + qryCdrLimit[1] + "]");
                    }
                    return true;
                }
            }
        }
        return false;
    }


    public boolean isUserStatusOK(CngUserSpData dtOneUserSPlist, Map<String, String> threadLocalMapList) {
        if (!getUsersInfoByUserIdfromcoherence(dtOneUserSPlist.getsUserID(), threadLocalMapList)) {
            return false;
        } else {
            //用户状态不正常
            if (!"10".equals(threadLocalMapList.get("STATUS"))) {
                //非全月停机
                if (("30".equals(threadLocalMapList.get("STATUS")) || "31".equals(threadLocalMapList.get("STATUS"))) && Long.parseLong(threadLocalMapList.get("STOP_DATE")) >=
                        Long.parseLong((String.valueOf(Montnetconfig.changeMonth.get()) + "01000000"))) {
                    return true;
                } else {
                    return false;
                }
            }
        }

        return true;
    }

    public boolean getUsersInfoByUserIdfromcoherence(long nUserID, Map<String, String> threadLocalMapList) {
        try {
            List<UserInfo> returnList = iUserInfoManage.getUserByUserId(CacheNameDef.CACHE_USERS, nUserID);

            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_DETAIL, "[" + TAG + "根据key:" + nUserID + "查询" + CacheNameDef.CACHE_USERS + "表][获取记录数:" + (returnList == null ? null : returnList.size() + "]")
                        + "]");
            }

            if (returnList != null && returnList.size() > 0) {
                threadLocalMapList.put("STOP_DATE", String.valueOf((returnList.get(0)).getStopDate()));
                threadLocalMapList.put("STATUS", String.valueOf((returnList.get(0)).getStatus()));
                threadLocalMapList.put("IMSI", String.valueOf((returnList.get(0)).getImsi()));
                threadLocalMapList.put("SERVICE_NUMBER", String.valueOf((returnList.get(0)).getServiceNumber()));
                resetUserInfo(nUserID, threadLocalMapList);
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            LogUtil.error("根据key:" + nUserID + "查询" + CacheNameDef.CACHE_USERS + "表异常，查询数据失败！]", e);
            return false;
        }
    }

    public void resetUserInfo(long nUserID, Map<String, String> threadLocalMapList) {
        if ("0".equals(threadLocalMapList.get("STOP_DATE")) || null == threadLocalMapList.get("STOP_DATE")) {
            threadLocalMapList.put("STOP_DATE", "0");
        }

        if ("".equals(threadLocalMapList.get("STATUS"))) {
            threadLocalMapList.put("STATUS", "0");
        }

        if ("0".equals(threadLocalMapList.get("IMSI")) || null == threadLocalMapList.get("IMSI")) {
            threadLocalMapList.put("IMSI", "");
        }


        LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "~user_id=" + nUserID + "~STOP_DATE=" + threadLocalMapList.get("STOP_DATE") + "~IMSI=" + threadLocalMapList.get("IMSI")
                + "~SERVICE_NUMBER=" + threadLocalMapList.get("SERVICE_NUMBER") + "STATUS=" + threadLocalMapList.get("STATUS"));

    }

    public boolean isPunishSpServ(CngUserSpData dtOneUserSPlist, DbResultInfo dbResultInfo) {
        if (dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()) == null) {
            return false;
        }

        for (int row = 0; row < dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).size(); row++) {
            if (isSpPunishEqual(row, dtOneUserSPlist, dbResultInfo)) {
                if (dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getnPUNISHTYPE() == 3)  //停业务  SPSERV_STOPSERV  3
                {
                    return true;
                } else if (dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getnPUNISHTYPE() == 2)  //SPSERV_STOPBILL  停计费 2
                {
                    return isNeedStopBill(dtOneUserSPlist, row, dbResultInfo);
                }
            }
        }

        return false;
    }

    public boolean isNeedStopBill(CngUserSpData dtOneUserSPlist, int row, DbResultInfo dbResultInfo) {
        //判断天数是否>=r_dayNum
        //计算当月有效天数
        if (getPunishValidDayNum(dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getsSTARTDATE(),
                dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getsENDDATE()) >= 10)  //处罚阀值:天  10  MCDR_PUNISHSP_DAYNUM
        {
            return true;
        } else {
            return false;
        }
    }

    public int getPunishValidDayNum(String sBeginDate, String sEndDate) {
        int nDayNum = 0;
        long nMonthDay, nBeginDay, nEndDay;

        nBeginDay = Long.parseLong(sBeginDate.substring(0, 7));
        nEndDay = Long.parseLong(sEndDate.substring(0, 7));
        nMonthDay = Montnetconfig.changeMonth.get() * 100 + 1;

        if (nMonthDay < nBeginDay) {
            nMonthDay = nBeginDay;
        }

        if (nEndDay >= nMonthDay) {
            //当月
            if (nEndDay / 100 == Montnetconfig.changeMonth.get()) {
                nDayNum = (int) ((nEndDay % 100) - (nMonthDay % 100) + 1);
            } else {
                nDayNum = 31;
            }
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "get punish valid day,nDayNum:" + nDayNum + "][begin:" + nMonthDay + "][end:" + nEndDay + "]");
        }

        return nDayNum;
    }

    public boolean isSpPunishEqual(int row, CngUserSpData dtOneUserSPlist, DbResultInfo dbResultInfo) {
        if (dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getsSPID().equals(dtOneUserSPlist.getsSpID()) &&
                (dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getsSPBIZID().equals(dtOneUserSPlist.getsSpBizID()) || dbResultInfo.getMapSpPunishList().get(dtOneUserSPlist.getsSpID()).get(row).getsSPBIZID().equals("*"))) {
            return true;
        }
        return false;
    }

    public boolean isSpNeedProcess(List<CngUserSpData> dtUserSPlistONE, boolean bCharge, CngMcdrChargeCfgData
                                           mcdrChargeCfgDT,
                                   List<String> m_fOrdTimeList, CngUserSpData t_orderInfo, CngExdSpCodeDefAllData
                                           threadLocalspInfo, DbResultInfo dbResultInfo) {
        //判断该SP是否需要处理
        if (!isNeedDealSp(dtUserSPlistONE, mcdrChargeCfgDT, threadLocalspInfo)) {
            if (LogUtil.isWarnEnable()) {
                LogUtil.warn(LogProperty.LOGTYPE_CALL + "[" + TAG + "该业务不需要处理USER_ID:" + dtUserSPlistONE.get(0).getsUserID() + " ],sp_biz_code:" + dtUserSPlistONE.get(0).getsSpBizID() + "]");
                return false;
            }
        }

        //判断是否需要生成，主要是有些业务会免费一段时间
        if (!isNeedCreateMcdr(dtUserSPlistONE, mcdrChargeCfgDT, m_fOrdTimeList, t_orderInfo, dbResultInfo)) {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "charge condition not valid]");
            }
            return false;
        }

        return true;
    }

    public boolean isNeedCreateMcdr(List<CngUserSpData> dtOneUserSPlist, CngMcdrChargeCfgData
            mcdrChargeCfgDT, List<String> m_fOrdTimeList, CngUserSpData t_orderInfo, DbResultInfo dbResultInfo) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---isNeedCreateMcdr]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "mcdrChargeCfgDT.getnCHARGEMODE():" + mcdrChargeCfgDT.getnCHARGEMODE() + "]");
        }

        boolean bRet = false;

        if (!isFirstMonthNoCharge(dtOneUserSPlist, dbResultInfo)) {
            return false;
        }

        //取个性化收费配置,个别业务和平台收费策略不一致时使用
        // add by yugf for BUG15_8049  立即扣费业务判断成72小时免费的问题
        long nOldChargeMode = mcdrChargeCfgDT.getnCHARGEMODE();
        long nNewChargeMode = mcdrChargeCfgDT.getnCHARGEMODE();

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "OLD charge_mode:" + nOldChargeMode + "]");
        }

        if (getSpChargeModeCfg(dtOneUserSPlist, mcdrChargeCfgDT, dbResultInfo)) {
            nNewChargeMode = mcdrChargeCfgDT.getnCHARGEMODE();
            mcdrChargeCfgDT.setnCHARGEMODE(nOldChargeMode);
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "NEW charge_mode:" + nNewChargeMode + "]");
        }

        bRet = isChargeModeEqual(nNewChargeMode, dtOneUserSPlist, m_fOrdTimeList, t_orderInfo);
        return bRet;
    }

    public boolean isChargeModeEqual(long nNewChargeMode, List<
            CngUserSpData> dtOneUserSPlist, List<String> m_fOrdTimeList, CngUserSpData t_orderInfo) {
        boolean bRet = false;
        switch (Integer.parseInt(String.valueOf(nNewChargeMode))) {
            case 1:
                bRet = isChargeMode1(dtOneUserSPlist, m_fOrdTimeList, t_orderInfo);
                break;
            case 2:
                bRet = isChargeMode2(dtOneUserSPlist, m_fOrdTimeList, t_orderInfo);
                break;
            case 3:
                bRet = isChargeMode3(dtOneUserSPlist, m_fOrdTimeList, t_orderInfo);
                break;
            case 4:
                bRet = isChargeMode4(dtOneUserSPlist, m_fOrdTimeList, t_orderInfo);
                break;
            case 5:
                bRet = isChargeMode5(dtOneUserSPlist, m_fOrdTimeList, t_orderInfo);
                break;
            case 6:
                bRet = isChargeMode6(dtOneUserSPlist, m_fOrdTimeList, t_orderInfo);
                break;
            default:
                bRet = false;
                break;
        }

        return bRet;
    }

    public boolean isChargeMode6
            (List<CngUserSpData> dtOneUserSPlist, List<String> mfOrdTimeList, CngUserSpData torderInfo) {
        mfOrdTimeList.clear();
        long tlab;
        boolean tbFlag;
        long tiBeginDate, tiBeginTime, tiEndDate, tiEndTime;
        long tiOrderTimes;
        long tnowDay, tnowTime;

        Date date = new Date();
        DateFormat formatrdealTime = new SimpleDateFormat("yyyyMMddHHmmss");
        String rdealTime = formatrdealTime.format(date);

        tnowDay = Long.parseLong(rdealTime.substring(0, 8));
        tnowTime = Long.parseLong(rdealTime.substring(8, rdealTime.length()));

        for (tlab = 0, tbFlag = false, tiOrderTimes = 0;
             (tlab < dtOneUserSPlist.size() && tbFlag == false);
             tlab++) {
            torderInfo.setnBillFlag(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getnBillFlag());
            torderInfo.setsDoMainID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsDoMainID());
            torderInfo.setsEndDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsEndDate());
            torderInfo.setsPriorOrderDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsPriorOrderDate());
            torderInfo.setsProdInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsProdInstanceID());
            torderInfo.setsSpBizID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpBizID());
            torderInfo.setsSpID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpID());
            torderInfo.setsSpType(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpType());
            torderInfo.setsStartDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsStartDate());
            torderInfo.setsUserID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserID());
            torderInfo.setInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getInstanceID());
            torderInfo.setsUserIDDone(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserIDDone());

            torderInfo.setsStartDate(torderInfo.getsPriorOrderDate().substring(0, torderInfo.getsStartDate().length()));
            torderInfo.setMarketId(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getMarketId());

            tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
            tiBeginTime = Long.parseLong(torderInfo.getsStartDate().substring(8, 14));
            tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));
            tiEndTime = Long.parseLong(torderInfo.getsEndDate().substring(8, 14));

            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_orderInfo.getsStartDate():" + torderInfo.getsStartDate() + "]");
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_nowDay:" + tnowDay + "][t_nowTime:" + tnowTime + "][t_iBeginDate:" + tiBeginDate + "][t_iBeginTime:" + tiBeginTime
                        + "][t_iEndDate:" + tiEndDate + "][t_iEndTime:" + tiEndTime + "]");
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "11:" + addDates(tiBeginDate, 4) + "][22:" + addDates(20100228, 4) + "][33:" + addDates(20101231, 4) + "]");
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_orderInfo.getsStartDate():" + torderInfo.getsStartDate() + "]");
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_orderInfo.getsEndDate():" + torderInfo.getsEndDate() + "]");
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_iBeginDate:" + tiBeginDate + "]");
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "Montnetconfig.changeMonth.get()*100+1:" + Montnetconfig.changeMonth.get() * 100 + 1 + "]");
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_iEndDate:" + tiEndDate);
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "Montnetconfig.changeMonth.get()*100+1:" + Montnetconfig.changeMonth.get() * 100 + 1 + "]");
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_iBeginDate/100:" + tiBeginDate / 100 + "]");
            }

            if ((tiBeginDate / 100) == Montnetconfig.changeMonth.get()) {
                if (!repeatOrderTime(torderInfo.getsPriorOrderDate(), mfOrdTimeList)) {
                    tiOrderTimes++;
                    mfOrdTimeList.add(torderInfo.getsPriorOrderDate());
                }
                if (isCharge61(tiOrderTimes, torderInfo)) {
                    return true;
                }
            } else {
                if (isCharge62(torderInfo)) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isCharge62(CngUserSpData torderInfo) {
        long tiBeginDate, tiBeginTime, tiEndDate, tiEndTime;

        tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
        tiBeginTime = Long.parseLong(torderInfo.getsStartDate().substring(8, 14));
        tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));
        tiEndTime = Long.parseLong(torderInfo.getsEndDate().substring(8, 14));

        long tnowDay, tnowTime;

        Date date = new Date();
        DateFormat formatrdealTime = new SimpleDateFormat("yyyyMMddHHmmss");
        String rdealTime = formatrdealTime.format(date);

        tnowDay = Long.parseLong(rdealTime.substring(0, 8));
        tnowTime = Long.parseLong(rdealTime.substring(8, rdealTime.length()));

        if (Long.parseLong(torderInfo.getsStartDate()) >= Long.parseLong(torderInfo.getsEndDate())) {
            return false;
        } else if ((tiBeginDate < Montnetconfig.changeMonth.get() * 100 + 1) &&
                (tiEndDate >= Montnetconfig.changeMonth.get() * 100 + 1)) {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_iEndDate:" + tiEndDate + "AddDates(t_iBeginDate,4):" + addDates(tiBeginDate, 4) + "]");
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_nowDay:" + tnowDay + "AddDates(t_iBeginDate,4)):" + addDates(tiBeginDate, 4) + "]");
            }
            if (isCharge6(torderInfo)) {
                return true;
            }
        }
        return false;
    }

    public boolean isCharge6(CngUserSpData torderInfo) {
        long tiBeginDate, tiBeginTime, tiEndDate, tiEndTime;
        tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
        tiBeginTime = Long.parseLong(torderInfo.getsStartDate().substring(8, 14));
        tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));
        tiEndTime = Long.parseLong(torderInfo.getsEndDate().substring(8, 14));

        long tnowDay, tnowTime;

        Date date = new Date();
        DateFormat formatrdealTime = new SimpleDateFormat("yyyyMMddHHmmss");
        String rdealTime = formatrdealTime.format(date);

        tnowDay = Long.parseLong(rdealTime.substring(0, 8));
        tnowTime = Long.parseLong(rdealTime.substring(8, rdealTime.length()));

        if ((tiEndDate > addDates(tiBeginDate, 3) || (tiEndDate == addDates(tiBeginDate, 3) && tiEndTime > tiBeginTime)) && (tnowDay > addDates(tiBeginDate, 3) || (tnowDay == addDates(tiBeginDate, 3) && tnowTime > tiBeginTime))) {
            return true;
        }
        return false;
    }

    public boolean isCharge61(long tiOrderTimes, CngUserSpData torderInfo) {
        long tiBeginDate, tiBeginTime, tiEndDate, tiEndTime;
        tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
        tiBeginTime = Long.parseLong(torderInfo.getsStartDate().substring(8, 14));
        tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));
        tiEndTime = Long.parseLong(torderInfo.getsEndDate().substring(8, 14));

        long tnowDay, tnowTime;

        Date date = new Date();
        DateFormat formatrdealTime = new SimpleDateFormat("yyyyMMddHHmmss");
        String rdealTime = formatrdealTime.format(date);

        tnowDay = Long.parseLong(rdealTime.substring(0, 8));
        tnowTime = Long.parseLong(rdealTime.substring(8, rdealTime.length()));

        if (tiOrderTimes > 1) {
            return true;
        } else {
            if (isTimeNeedCharge(tiEndDate, tiBeginDate, tiEndTime, tiBeginTime, tnowDay, tnowTime)) {
                return true;
            }
        }
        return false;
    }

    public boolean isTimeNeedCharge(long tiEndDate, long tiBeginDate, long tiEndTime, long tiBeginTime, long tnowDay, long tnowTime) {
        if ((tiEndDate > addDates(tiBeginDate, 3) || (tiEndDate == addDates(tiBeginDate, 3) && tiEndTime > tiBeginTime)) && (tnowDay > addDates(tiBeginDate, 3) || (tnowDay == addDates(tiBeginDate, 3) && tnowTime > tiBeginTime))) {
            return true;
        }
        return false;
    }

    public boolean isChargeMode5
            (List<CngUserSpData> dtOneUserSPlist, List<String> mfOrdTimeList, CngUserSpData torderInfo) {
        long tlab;
        boolean tbFlag;
        long tiBeginDate, tiEndDate;

        for (tlab = 0, tbFlag = false;
             (tlab < dtOneUserSPlist.size() && tbFlag == false);
             tlab++) {
            torderInfo.setnBillFlag(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getnBillFlag());
            torderInfo.setsDoMainID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsDoMainID());
            torderInfo.setsEndDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsEndDate());
            torderInfo.setsPriorOrderDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsPriorOrderDate());
            torderInfo.setsProdInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsProdInstanceID());
            torderInfo.setsSpBizID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpBizID());
            torderInfo.setsSpID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpID());
            torderInfo.setsSpType(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpType());
            torderInfo.setsStartDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsStartDate());
            torderInfo.setsUserID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserID());
            torderInfo.setInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getInstanceID());
            torderInfo.setsUserIDDone(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserIDDone());

            torderInfo.setsStartDate(torderInfo.getsPriorOrderDate());
            torderInfo.setMarketId(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getMarketId());

            tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
            tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));

            doSpecialCharge(torderInfo, tiBeginDate, tiEndDate);

            if (Long.parseLong(torderInfo.getsStartDate()) >= Long.parseLong(torderInfo.getsEndDate())) {
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "失效时间在前，则跳过" + "1111111111111]");
                }
                continue;
            } else if ((tiBeginDate < Montnetconfig.changeMonth.get() * 100 + 1) &&
                    (tiEndDate >= Montnetconfig.changeMonth.get() * 100 + 1)) {
                tbFlag = true;
            } else if ((tiBeginDate / 100) == Montnetconfig.changeMonth.get()) {
                tbFlag = true;
            }
        }
        return tbFlag;
    }

    public void doSpecialCharge(CngUserSpData torderInfo, long tiBeginDate, long tiEndDate) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_orderInfo.getsStartDate():" + torderInfo.getsStartDate() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_orderInfo.getsEndDate():" + torderInfo.getsEndDate() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_iBeginDate:" + tiBeginDate + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "Montnetconfig.changeMonth.get()*100+1:" + (Montnetconfig.changeMonth.get() * 100 + 1) + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_iEndDate:" + tiEndDate + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "失效时间在前，则跳过:" + "1111111111111]");
        }

        if (torderInfo.getsSpID().equals("910477")) {
            DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
            try {
                Date date = df.parse(torderInfo.getsEndDate());
                date.setTime(date.getTime() + 3 * 60 * 60 * 1000);
                torderInfo.setsEndDate(df.format(date));
            } catch (ParseException e) {
                LogUtil.error("时间格式转换异常", e);
            }
        }
    }

    public boolean isChargeMode4
            (List<CngUserSpData> dtOneUserSPlist, List<String> mfOrdTimeList, CngUserSpData torderInfo) {
        mfOrdTimeList.clear();
        long tlab;
        boolean tbFlag;
        long tiBeginDate, tiBeginTime, tiEndDate, tiEndTime;
        long tiOrderTimes;

        for (tlab = 0, tbFlag = false, tiOrderTimes = 0;
             (tlab < dtOneUserSPlist.size() && tbFlag == false);
             tlab++) {
            torderInfo.setnBillFlag(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getnBillFlag());
            torderInfo.setsDoMainID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsDoMainID());
            torderInfo.setsEndDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsEndDate());
            torderInfo.setsPriorOrderDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsPriorOrderDate());
            torderInfo.setsProdInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsProdInstanceID());
            torderInfo.setsSpBizID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpBizID());
            torderInfo.setsSpID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpID());
            torderInfo.setsSpType(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpType());
            torderInfo.setsStartDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsStartDate());
            torderInfo.setsUserID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserID());
            torderInfo.setInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getInstanceID());
            torderInfo.setsUserIDDone(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserIDDone());


            torderInfo.setsStartDate(torderInfo.getsPriorOrderDate().substring(0, torderInfo.getsStartDate().length() - 1));
            torderInfo.setMarketId(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getMarketId());

            tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
            tiBeginTime = Long.parseLong(torderInfo.getsStartDate().substring(8, 13));
            tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));
            tiEndTime = Long.parseLong(torderInfo.getsEndDate().substring(8, 13));

            if (Long.parseLong(torderInfo.getsStartDate()) >= Long.parseLong(torderInfo.getsEndDate())) {
                continue;
            } else if ((tiBeginDate < Montnetconfig.changeMonth.get() * 100 + 1) &&
                    (tiEndDate >= Montnetconfig.changeMonth.get() * 100 + 1)) {
                tbFlag = true;
            } else if ((tiBeginDate / 100) == Montnetconfig.changeMonth.get()) {
                if (repeatOrderTime(torderInfo.getsPriorOrderDate(), mfOrdTimeList) == false) {
                    tiOrderTimes++;
                    mfOrdTimeList.add(torderInfo.getsPriorOrderDate());
                }

                tbFlag = isCharge4(tiOrderTimes, torderInfo);
            }

        }
        return tbFlag;
    }

    public boolean isCharge4(long tiOrderTimes, CngUserSpData torderInfo) {
        long tiBeginDate, tiBeginTime, tiEndDate, tiEndTime;
        long tnowDay, tnowTime;
        tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
        tiBeginTime = Long.parseLong(torderInfo.getsStartDate().substring(8, 13));
        tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));
        tiEndTime = Long.parseLong(torderInfo.getsEndDate().substring(8, 13));


        Date date = new Date();
        DateFormat formatrdealTime = new SimpleDateFormat("yyyyMMddHHmmss");
        String rdealTime = formatrdealTime.format(date);

        tnowDay = Long.parseLong(rdealTime.substring(0, 7));
        tnowTime = Long.parseLong(rdealTime.substring(8));

        if (tiOrderTimes > 1) {
            return true;
        } else {
            if (isEffect(tiBeginDate, tiBeginTime, tiEndDate, tiEndTime, tnowDay, tnowTime)) {
                return true;
            }
        }
        return false;
    }

    public boolean isEffect(long tiBeginDate, long tiBeginTime, long tiEndDate, long tiEndTime, long tnowDay,
                            long tnowTime) {
        if ((tiEndDate > addDates(tiBeginDate, 1) || (tiEndDate == addDates(tiBeginDate, 1) && tiEndTime > tiBeginTime)) && (tnowDay > addDates(tiBeginDate, 1) || (tnowDay == addDates(tiBeginDate, 1) && tnowTime > tiBeginTime))) {
            return true;
        }
        return false;
    }

    public long addDates(long nYearDate, int nModNum) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        Date tmpStartTime = null;
        try {
            tmpStartTime = format.parse(String.valueOf(nYearDate));
        } catch (ParseException e) {
            LogUtil.error("时间转换异常", e);
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(tmpStartTime);
        cal.add(Calendar.DATE, nModNum);
        long beginDate = Long.parseLong(new SimpleDateFormat("yyyyMMdd").format(cal.getTime()));
        return beginDate;
    }

    public boolean isChargeMode3
            (List<CngUserSpData> dtOneUserSPlist, List<String> mfOrdTimeList, CngUserSpData torderInfo) {
        long tlab;
        boolean tbFlag;
        long tiBeginDate, tiEndDate;

        for (tlab = 0, tbFlag = false;
             (tlab < dtOneUserSPlist.size() && tbFlag == false);
             tlab++) {
            torderInfo.setnBillFlag(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getnBillFlag());
            torderInfo.setsDoMainID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsDoMainID());
            torderInfo.setsEndDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsEndDate());
            torderInfo.setsPriorOrderDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsPriorOrderDate());
            torderInfo.setsProdInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsProdInstanceID());
            torderInfo.setsSpBizID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpBizID());
            torderInfo.setsSpID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpID());
            torderInfo.setsSpType(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpType());
            torderInfo.setsStartDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsStartDate());
            torderInfo.setsUserID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserID());
            torderInfo.setInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getInstanceID());
            torderInfo.setsUserIDDone(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserIDDone());

            torderInfo.setsStartDate(torderInfo.getsPriorOrderDate().substring(0, torderInfo.getsStartDate().length() - 1));
            torderInfo.setMarketId(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getMarketId());

            tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
            tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));

            if (Long.parseLong(torderInfo.getsStartDate()) >= Long.parseLong(torderInfo.getsEndDate())) {
                continue;
            } else if ((tiBeginDate < Montnetconfig.changeMonth.get() * 100 + 1) &&
                    (tiEndDate >= Montnetconfig.changeMonth.get() * 100 + 1)) {
                tbFlag = true;
            } else if ((tiBeginDate / 100) == Montnetconfig.changeMonth.get()) {
                if (tiBeginDate % 100 < 21) {
                    tbFlag = true;
                }
            }
        }
        return tbFlag;
    }

    public boolean isChargeMode2
            (List<CngUserSpData> dtOneUserSPlist, List<String> mfOrdTimeList, CngUserSpData torderInfo) {
        long tlab;
        boolean tbFlag;
        long tiBeginDate, tiEndDate;
        long tpreMonth;

        tpreMonth = addMonths(Montnetconfig.changeMonth.get(), (long) -1);

        for (tlab = 0, tbFlag = false;
             (tlab < dtOneUserSPlist.size() && tbFlag == false);
             tlab++) {
            torderInfo.setnBillFlag(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getnBillFlag());
            torderInfo.setsDoMainID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsDoMainID());
            torderInfo.setsEndDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsEndDate());
            torderInfo.setsPriorOrderDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsPriorOrderDate());
            torderInfo.setsProdInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsProdInstanceID());
            torderInfo.setsSpBizID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpBizID());
            torderInfo.setsSpID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpID());
            torderInfo.setsSpType(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpType());
            torderInfo.setsStartDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsStartDate());
            torderInfo.setsUserID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserID());
            torderInfo.setInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getInstanceID());
            torderInfo.setsUserIDDone(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserIDDone());

            torderInfo.setsStartDate(torderInfo.getsPriorOrderDate().substring(0, torderInfo.getsStartDate().length() - 1));
            torderInfo.setMarketId(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getMarketId());

            tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
            tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));

            if (Long.parseLong(torderInfo.getsStartDate()) >= Long.parseLong(torderInfo.getsEndDate())) {
                continue;
            } else if (tiEndDate >= Montnetconfig.changeMonth.get() * 100 + 1) {
                if ((tiBeginDate / 100) < tpreMonth) {
                    return true;
                } else if (((tiBeginDate / 100) == tpreMonth) &&
                        ((tiBeginDate % 100) < 21)) {
                    return true;
                }
            }
        }
        return false;
    }

    public long addMonths(long nBillMonth, long nAddMonths) {
        long nTotalMonths = nBillMonth / 100 * 12 + nBillMonth % 100;
        nTotalMonths += nAddMonths;
        long nTmp = nTotalMonths / 12 * 100 + nTotalMonths % 12;
        if (0 == nTmp % 100) {
            nTmp = (nTmp / 100 - 1) * 100 + 12;
        }
        return nTmp;
    }

    public boolean isChargeMode1
            (List<CngUserSpData> dtOneUserSPlist, List<String> mfOrdTimeList, CngUserSpData torderInfo) {
        mfOrdTimeList.clear();
        long tlab;
        boolean tbFlag;
        long tiBeginDate;

        long tiOrderTimes;

        for (tlab = 0, tbFlag = false, tiOrderTimes = 0;
             (tlab < dtOneUserSPlist.size() && tbFlag == false);
             tlab++) {
            torderInfo.setnBillFlag(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getnBillFlag());
            torderInfo.setsDoMainID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsDoMainID());
            torderInfo.setsEndDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsEndDate());
            torderInfo.setsPriorOrderDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsPriorOrderDate());
            torderInfo.setsProdInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsProdInstanceID());
            torderInfo.setsSpBizID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpBizID());

            if (torderInfo.getsSpBizID() == null || torderInfo.getsSpBizID().equals("null")) {
                torderInfo.setsSpBizID("");
            }

            torderInfo.setsSpID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpID());
            torderInfo.setsSpType(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsSpType());
            torderInfo.setsStartDate(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsStartDate());
            torderInfo.setsUserID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserID());
            torderInfo.setInstanceID(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getInstanceID());
            torderInfo.setsUserIDDone(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getsUserIDDone());
            torderInfo.setsStartDate(torderInfo.getsPriorOrderDate().substring(0, torderInfo.getsStartDate().length() - 1));
            torderInfo.setMarketId(dtOneUserSPlist.get(Integer.parseInt(String.valueOf(tlab))).getMarketId());

            tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));

            if ((tiBeginDate / 100) == Montnetconfig.changeMonth.get()) {
                //当月定购
                if (repeatOrderTime(torderInfo.getsPriorOrderDate(), mfOrdTimeList) == false) {
                    tiOrderTimes++;
                    mfOrdTimeList.add(torderInfo.getsPriorOrderDate());
                }

                tbFlag = ischarge1(tiOrderTimes, torderInfo);
            } else {
                if (ischarge11(torderInfo)) {
                    tbFlag = true;
                }

            }
        }

        return tbFlag;
    }

    public boolean ischarge11(CngUserSpData torderInfo) {
        long tiBeginDate, tiEndDate;
        tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));
        tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));

        if (Long.parseLong(torderInfo.getsStartDate()) >= Long.parseLong(torderInfo.getsEndDate())) {
            //失效时间在前，则跳过
            return false;
        } else if ((tiBeginDate < Montnetconfig.changeMonth.get() * 100 + 1) &&
                (tiEndDate >= Montnetconfig.changeMonth.get() * 100 + 1)) {
            //1. 是否有非当月定购且当月曾有效的,有则直接收费
            return true;
        }
        return false;
    }

    public boolean ischarge1(long tiOrderTimes, CngUserSpData torderInfo) {
        long tmonthFirstDay = 0;
        long tiBeginDate, tiBeginTime, tiEndDate, tiEndTime;
        tiEndDate = Long.parseLong(torderInfo.getsEndDate().substring(0, 8));
        tiEndTime = Long.parseLong(torderInfo.getsEndDate().substring(8, 13));

        tiBeginTime = Long.parseLong(torderInfo.getsStartDate().substring(8, 13));

        long tnowDay, tnowTime;

        Date date = new Date();
        DateFormat formatrdealTime = new SimpleDateFormat("yyyyMMddHHmmss");
        String rdealTime = formatrdealTime.format(date);

        tnowDay = Long.parseLong(rdealTime.substring(0, 7));
        tnowTime = Long.parseLong(rdealTime.substring(8));

        tiBeginDate = Long.parseLong(torderInfo.getsStartDate().substring(0, 8));

        if (tiOrderTimes == 1) {
            tmonthFirstDay = tiBeginDate % 100;
        } else {
            if (tiBeginDate % 100 < tmonthFirstDay) {
                tmonthFirstDay = tiBeginDate % 100;
            }
        }

        if (tmonthFirstDay < 21) {
            if (tiOrderTimes > 1) {
                return true;
            } else {
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "t_nowDay:" + tnowDay + "][t_iBeginDate:" + tiBeginDate + "][t_nowTime:" + tnowTime + "][t_iBeginTime:" + tiBeginTime + "]");
                }
                if (isMoreThanFourDays(tiBeginDate, tiBeginTime, tiEndDate, tiEndTime, tnowDay, tnowTime)) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isMoreThanFourDays(long tiBeginDate, long tiBeginTime, long tiEndDate, long tiEndTime,
                                      long tnowDay, long tnowTime) {
        if ((tiEndDate > tiBeginDate + 4 || (tiEndDate == tiBeginDate + 4 && tiEndTime > tiBeginTime)) && (tnowDay > tiBeginDate + 4 || (tnowDay == tiBeginDate + 4 && tnowTime > tiBeginTime))) {
            return true;
        }
        return false;
    }

    public boolean repeatOrderTime(String r_orderTime, List<String> m_fOrdTimeList) {
        for (int row = 0; row < m_fOrdTimeList.size(); row++) {
            if (r_orderTime.equals(m_fOrdTimeList.get(row))) {
                return true;
            }
        }
        return false;
    }

    public boolean getSpChargeModeCfg(List<CngUserSpData> dtOneUserSPlist, CngMcdrChargeCfgData
            mcdrChargeCfgDT, DbResultInfo dbResultInfo) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---getSpChargeModeCfg]");
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtOneUserSPlist.get(0)" + dtOneUserSPlist.get(0));
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "CngMcdrSpChargeModeCfgData.mapMcdrSpChargeModeCfgList.get( dtOneUserSPlist.get(0).getsSpID()):" + ArrayUtils.toString(dbResultInfo.getMapMcdrSpChargeModeCfgList().get(dtOneUserSPlist.get(0).getsSpID())));
        }

        if (dbResultInfo.getMapMcdrSpChargeModeCfgList().get(dtOneUserSPlist.get(0).getsSpID()) == null) {
            return false;
        }

        for (int row = 0; row < dbResultInfo.getMapMcdrSpChargeModeCfgList().get(dtOneUserSPlist.get(0).getsSpID()).size(); row++) {
            if (checkMapCdrSpMode(dtOneUserSPlist, row, dbResultInfo)) {
                mcdrChargeCfgDT.setnCHARGEMODE(dbResultInfo.getMapMcdrSpChargeModeCfgList().get(dtOneUserSPlist.get(0).getsSpID()).get(row).getnCHARGEMODE());
                return true;
            }
        }

        return false;
    }

    public boolean checkMapCdrSpMode(List<CngUserSpData> dtOneUserSPlist, int row, DbResultInfo dbResultInfo) {
        if (dbResultInfo.getMapMcdrSpChargeModeCfgList().get(dtOneUserSPlist.get(0).getsSpID()).get(row).getStrSPCODE().equals(dtOneUserSPlist.get(0).getsSpID()) &&
                (dbResultInfo.getMapMcdrSpChargeModeCfgList().get(dtOneUserSPlist.get(0).getsSpID()).get(row).getStrOPERCODE().equals(dtOneUserSPlist.get(0).getsSpBizID()) ||
                        dbResultInfo.getMapMcdrSpChargeModeCfgList().get(dtOneUserSPlist.get(0).getsSpID()).get(row).getStrOPERCODE().equals("*"))) {
            return true;
        }
        return false;
    }


    public boolean isFirstMonthNoCharge(List<CngUserSpData> dtOneUserSPlist, DbResultInfo dbResultInfo) {
        if (dbResultInfo.getMapFirstMonthNoChargeBusi().containsKey(dtOneUserSPlist.get(0).getsSpID() + "~" + dtOneUserSPlist.get(0).getsSpBizID())) {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "找到开通首月不收费业务,busi:" + dtOneUserSPlist.get(0).getsSpID() + "~" + dtOneUserSPlist.get(0).getsSpBizID() + "]");
            }
            int i = 0;
            for (i = 0; i < dtOneUserSPlist.size(); i++) {
                if (!String.valueOf(Montnetconfig.changeMonth.get()).equals(dtOneUserSPlist.get(i).getsPriorOrderDate().substring(0, 6))) {
                    if (LogUtil.isDebugEnable()) {
                        LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "该用户该业务不是当月首次开通,订购时间:" + dtOneUserSPlist.get(i).getsPriorOrderDate() + "]");
                    }
                    break;
                }
            }

            if (i >= dtOneUserSPlist.size()) {
                if (LogUtil.isDebugEnable()) {
                    LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "该用户该业务开通首月不收费,user_id:" + dtOneUserSPlist.get(0).getsUserID() + "][sp_code:" + dtOneUserSPlist.get(0).getsSpID() + "][oper_code:" + dtOneUserSPlist.get(0).getsSpBizID() + "]");
                }
                return false;
            }
        }
        return true;
    }


    public boolean isNeedDealSp(List<CngUserSpData> dtOneUserSPlist, CngMcdrChargeCfgData
            mcdrChargeCfgDT, CngExdSpCodeDefAllData threadLocalspInfo) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---isNeedDealSp]");
        }

        if (dtOneUserSPlist.get(0).getsSpID().length() < 3) {
            return false;
        }

        if ("G".equals(threadLocalspInfo.getStrSpAttr())) {
            return isQuanWangSp(mcdrChargeCfgDT, threadLocalspInfo);
        } else if ("L".equals(threadLocalspInfo.getStrSpAttr())) {
            return isLocalSp(mcdrChargeCfgDT);
        }

        return false;
    }

    public boolean isLocalSp(CngMcdrChargeCfgData mcdrChargeCfgDT) {
        if (mcdrChargeCfgDT.getnBusinessType() == 1 || mcdrChargeCfgDT.getnBusinessType() == 2) //   省内业务 1   ;省内和全网业务 2
        {
            return true;
        } else {
            return false;
        }
    }


    public boolean isQuanWangSp(CngMcdrChargeCfgData mcdrChargeCfgDT, CngExdSpCodeDefAllData threadLocalspInfo) {
        if (threadLocalspInfo.getnProvCode() == 250) {
            if (mcdrChargeCfgDT.getnBusinessType() == 1 || mcdrChargeCfgDT.getnBusinessType() == 2) {
                return true;
            } else {
                return false;
            }
        } else {
            if (mcdrChargeCfgDT.getnBusinessType() == 0 || mcdrChargeCfgDT.getnBusinessType() == 2) {
                return true;
            } else {
                return false;
            }
        }
    }


    public boolean isNeedProcess(List<CngUserSpData> dtUserSPlistONE, CngMcdrChargeCfgData
                                         mcdrChargeCfgDT, DbResultInfo dbResultInfo,
                                 CngExdSpCodeDefAllData threadLocalspInfo, CngExdSpOperDefAllData threadLocaloperInfo) {
        if (dtUserSPlistONE.size() == 0) {
            return false;
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---ProcessMcdr" + dtUserSPlistONE.size() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "****user_id:" + dtUserSPlistONE.get(0).getsUserID() + " ][sp_id:" + dtUserSPlistONE.get(0).getsSpID() + "][biz_id:" + dtUserSPlistONE.get(0).getsSpBizID() + "]");
        }

        //取SP信息|取SP业务定义信息|取业务配置信息
        if (!getSpCodeCfgBySpCode(dtUserSPlistONE, dbResultInfo, threadLocalspInfo) || !getSpOperbyOperCode(dtUserSPlistONE, dbResultInfo, threadLocaloperInfo)
                || !getSpChargeCfg(dtUserSPlistONE, mcdrChargeCfgDT, dbResultInfo, threadLocaloperInfo)) {
            return false;
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "mcdrChargeCfgDT.getnCHARGEMODE():" + mcdrChargeCfgDT.getnCHARGEMODE() + "]");
        }
        return true;
    }

    public boolean getSpOperbyOperCode(List<CngUserSpData> dtOneUserSPlist, DbResultInfo
            dbResultInfo, CngExdSpOperDefAllData threadLocaloperInfo) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---getSpOperbyOperCode]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "SP_ID:" + dtOneUserSPlist.get(0).getsSpID() + "][sp_biz_id:" + dtOneUserSPlist.get(0).getsSpBizID() + "]");
        }

        StringBuffer sIndex = new StringBuffer();
        sIndex.append(dtOneUserSPlist.get(0).getsSpID());
        sIndex.append(dtOneUserSPlist.get(0).getsSpBizID());

        if (dbResultInfo.getMapSpOperInfo().get(sIndex.toString()) == null) {
            if (LogUtil.isDebugEnable()) {
                LogUtil.debug("DTL" + TAG + "[没有找到SP业务配置信息sp_code+oper_code:" + sIndex.toString() + "]");
            }
            return false;
        }

        for (int row = 0; row < dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).size(); row++) {
            threadLocaloperInfo.setStrSERVTYPE(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrSERVTYPE());
            threadLocaloperInfo.setStrSPCODE(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrSPCODE());
            threadLocaloperInfo.setStrOPERCODE(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrOPERCODE());
            threadLocaloperInfo.setStrOPERNAME(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrOPERNAME());
            threadLocaloperInfo.setStrBILLFLAG(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrBILLFLAG());
            threadLocaloperInfo.setnFEE(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getnFEE());
            threadLocaloperInfo.setStrVALIDDATE(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrVALIDDATE());
            threadLocaloperInfo.setStrEXPIREDATE(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrEXPIREDATE());
            threadLocaloperInfo.setnINPROP(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getnINPROP());
            threadLocaloperInfo.setnOUTPROP(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getnOUTPROP());
            threadLocaloperInfo.setnCOUNT(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getnCOUNT());
            threadLocaloperInfo.setStrSPATTR(dbResultInfo.getMapSpOperInfo().get(sIndex.toString()).get(0).getStrSPATTR());

            resetFeeByMemberAttr(dtOneUserSPlist.get(0).getsUserID(), sIndex.toString(), threadLocaloperInfo, dbResultInfo);

            return true;
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug("[没有找到SP业务配置信息sp_code+oper_code:" + sIndex + "]");
        }

        return false;
    }

    public boolean getSpChargeCfg(List<CngUserSpData> dtOneUserSPlist, CngMcdrChargeCfgData
            mcdrChargeCfgDT, DbResultInfo dbResultInfo, CngExdSpOperDefAllData threadLocaloperInfo) {
        printSpTypeInfo(dtOneUserSPlist, threadLocaloperInfo);
        int y = 0;

        List<String> vSpType = new ArrayList<>();

        for (int row = 0; row < dbResultInfo.getDtMcdrChargeCfgList().size(); row++) {
            printMcdrInfo(row, dbResultInfo, threadLocaloperInfo);
            if (threadLocaloperInfo.getStrSERVTYPE().equals(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSERVTYPE())) {
                vSpType.clear();
                String[] strTmp = dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSPTYPE().split("\\,", -1);

                for (int col = 0; col < strTmp.length; col++) {
                    vSpType.add(strTmp[col]);
                }

                for (y = 0; y < vSpType.size(); y++) {
                    if (dtOneUserSPlist.get(0).getsSpType().equals(vSpType.get(y))) {
                        printVspTypeInfo(vSpType, y, dtOneUserSPlist);
                        mcdrChargeCfgDT.setnBusinessType(dbResultInfo.getDtMcdrChargeCfgList().get(row).getnBusinessType());
                        mcdrChargeCfgDT.setnCHARGEMODE(dbResultInfo.getDtMcdrChargeCfgList().get(row).getnCHARGEMODE());
                        mcdrChargeCfgDT.setnFILEMAXNUM(dbResultInfo.getDtMcdrChargeCfgList().get(row).getnFILEMAXNUM());
                        mcdrChargeCfgDT.setnLineNum(dbResultInfo.getDtMcdrChargeCfgList().get(row).getnLineNum());
                        mcdrChargeCfgDT.setsCREATEDATE(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsCREATEDATE());
                        mcdrChargeCfgDT.setsDOMAINID(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsDOMAINID());
                        mcdrChargeCfgDT.setsFileName(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsFileName());
                        mcdrChargeCfgDT.setsFILENAMEPRIX(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsFILENAMEPRIX());
                        mcdrChargeCfgDT.setsSERVNAME(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSERVNAME());
                        mcdrChargeCfgDT.setsSERVTYPE(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSERVTYPE());
                        mcdrChargeCfgDT.setsSingularStatus(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSingularStatus());
                        mcdrChargeCfgDT.setsSPTYPE(dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSPTYPE());


                        return true;
                    }
                }
            }
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "该业务没有找到收费配置sp_type:" + dtOneUserSPlist.get(0).getsSpType() + "][serv_type:" + threadLocaloperInfo.getStrSERVTYPE() +
                    " ][domain_id:" + dtOneUserSPlist.get(0).getsDoMainID() + "]");
        }
        return false;
    }

    public void printVspTypeInfo(List<String> vSpType, int y, List<CngUserSpData> dtOneUserSPlist) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----dtOneUserSPlist.get(0).getsSpType():" + dtOneUserSPlist.get(0).getsSpType() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----vSpType.get(y):" + vSpType.get(y) + "]");
        }
    }

    public void printMcdrInfo(int row, DbResultInfo dbResultInfo, CngExdSpOperDefAllData threadLocaloperInfo) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----operInfo.getStrSERVTYPE():" + threadLocaloperInfo.getStrSERVTYPE() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----cngMcdrChargeCfgDT.dtMcdrChargeCfgList.get(row).getsSERVTYPE():" + dbResultInfo.getDtMcdrChargeCfgList().get(row).getsSERVTYPE() + "]");
        }
    }

    public void printSpTypeInfo(List<CngUserSpData> dtOneUserSPlist, CngExdSpOperDefAllData threadLocaloperInfo) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "-----start---work---getSpChargeCfg]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "operInfo.getStrSERVTYPE():" + threadLocaloperInfo.getStrSERVTYPE() + "]");
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtOneUserSPlist.get(0).getsSpType():" + dtOneUserSPlist.get(0).getsSpType() + "]");
        }
    }

    public void resetFeeByMemberAttr(long userId, String sIndex, CngExdSpOperDefAllData
            threadLocaloperInfo, DbResultInfo dbResultInfo) {
        {
            if (!"698039".equals(threadLocaloperInfo.getStrSPCODE())) {
                return;
            }

            int memberLevel = 0;

            memberLevel = getUserServiceInfoByUserId(userId);

            //sp_code=698039, oper_code = 698039018080000003特殊处理，打7折
            if (memberLevel == 4) {

                resetFeeByMemberMusic(sIndex, dbResultInfo, threadLocaloperInfo);
                return;
            }

            if (!dbResultInfo.getMapSpMusicAttr().containsKey(sIndex)) {
                return;
            }

            for (int row = 0; row < dbResultInfo.getMapSpMusicAttr().get(sIndex).size(); row++) {
                if (memberLevel == dbResultInfo.getMapSpMusicAttr().get(sIndex).get(row).getMemberAttr()) {
                    threadLocaloperInfo.setnFEE(dbResultInfo.getMapSpMusicAttr().get(sIndex).get(row).getnFEE());
                    break;
                }
            }
        }
    }

    public void resetFeeByMemberMusic(String sIndex, DbResultInfo dbResultInfo, CngExdSpOperDefAllData
            threadLocaloperInfo) {
        if (dbResultInfo.getMapSpMemberAttr().containsKey(sIndex)) {
            for (int row = 0; row < dbResultInfo.getMapSpMemberAttr().get(sIndex).size(); row++) {
                if (3 == dbResultInfo.getMapSpMemberAttr().get(sIndex).get(row).getMemberAttr()) {
                    threadLocaloperInfo.setnFEE(dbResultInfo.getMapSpMemberAttr().get(sIndex).get(row).getnFEE());
                    return;
                }
            }
        }

        if (dbResultInfo.getMapSpMusicAttr().containsKey(sIndex)) {
            for (int row = 0; row < dbResultInfo.getMapSpMusicAttr().get(sIndex).size(); row++) {
                if (3 == dbResultInfo.getMapSpMusicAttr().get(sIndex).get(row).getMemberAttr()) {
                    threadLocaloperInfo.setnFEE(dbResultInfo.getMapSpMusicAttr().get(sIndex).get(row).getnFEE());
                    return;
                }
            }
        }
    }

    public int getUserServiceInfoByUserId(long userId) {
        int memberLevel = 0;

        try {
            List<UserServiceInfo> returnList = iUserInfoManage.getUserServiceInfoByUserId(CacheNameDef.CACHE_USER_SERVICE, userId);
            if (returnList != null) {
                memberLevel = getMemberLevel(returnList);
            }

        } catch (Exception e) {
            LogUtil.error("[" + TAG + "根据key:" + userId + "查询" + CacheNameDef.CACHE_USER_SERVICE + "表异常，查询数据失败！]", e);
            return -1;
        }
        return memberLevel;
    }

    public int getMemberLevel(List<UserServiceInfo> returnList) {
        Date date = new Date();
        DateFormat formatDate = new SimpleDateFormat("yyyyMMddhhmmss");
        long nowDate = Long.parseLong(formatDate.format(date));

        int memberLevel = 0;

        for (int row = 0; row < returnList.size(); row++) {
            UserServiceInfo userServiceInfo = returnList.get(row);
            if (userServiceInfo.getStartDate() <= nowDate && userServiceInfo.getEndDate() >= nowDate) {
                //高级会员服务编号
                if (userServiceInfo.getServiceCode() == 120000025) {
                    memberLevel = 2;
                    break;
                }
                //普通会员服务编号
                else if (userServiceInfo.getServiceCode() == 120000026) {
                    memberLevel = 1;
                    break;
                }
                //特级会员服务代码
                else if (userServiceInfo.getServiceCode() == 120000262) {
                    memberLevel = 3;
                    break;
                } else if (userServiceInfo.getServiceCode() == 2400000261L) {
                    memberLevel = 4;
                    break;
                }
            }
        }
        return memberLevel;
    }

    public boolean getSpCodeCfgBySpCode(List<CngUserSpData> dtOneUserSPlist, DbResultInfo
            dbResultInfo, CngExdSpCodeDefAllData threadLocalspInfo) {
        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_CALL, "[" + TAG + "dtOneUserSPlist.get(0).getsSpID() :" + dtOneUserSPlist.get(0).getsSpID() + "]");
        }

        if (dbResultInfo.getMapSpInfo().containsKey(dtOneUserSPlist.get(0).getsSpID())) {
            List<CngExdSpCodeDefAllData> listData = dbResultInfo.getMapSpInfo().get(dtOneUserSPlist.get(0).getsSpID());

            threadLocalspInfo.setStrServType(listData.get(0).getStrServType());
            threadLocalspInfo.setStrSpCode(listData.get(0).getStrSpCode());
            threadLocalspInfo.setStrSpName(listData.get(0).getStrSpName());
            threadLocalspInfo.setStrSpType(listData.get(0).getStrSpType());
            threadLocalspInfo.setStrServCode(listData.get(0).getStrServCode());
            threadLocalspInfo.setnProvCode(listData.get(0).getnProvCode());
            threadLocalspInfo.setnBalProv(listData.get(0).getnBalProv());
            threadLocalspInfo.setStrDevCode(listData.get(0).getStrDevCode());
            threadLocalspInfo.setStrValidDate(listData.get(0).getStrValidDate());
            threadLocalspInfo.setStrExpireDate(listData.get(0).getStrExpireDate());
            threadLocalspInfo.setStrDescRiption(listData.get(0).getStrDescRiption());
            threadLocalspInfo.setStrSpAttr(listData.get(0).getStrSpAttr());
            return true;
        }

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug("[没有找到SP代码配置信息sp_code:" + dtOneUserSPlist.get(0).getsSpID() + "]");
        }
        return false;
    }

    public List<Object> sendMessage(List<CngUserSpData> dtOneOperListSend) {
        if (dtOneOperListSend.size() == 0) {
            return null;
        }
        List<Object> dealList = new ArrayList<>();
        dealList.add("normal");
        dealList.add(true);
        dealList.add(dtOneOperListSend);

        if (LogUtil.isDebugEnable()) {
            LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "发送给工作线程 sendMessage : " + dealList);
            LogUtil.debug(LogProperty.LOGTYPE_DETAIL, TAG + "发送给工作线程 dtOneOperListSend : " + ArrayUtils.toString(dtOneOperListSend));
        }
        return dealList;
    }
}
