# MonthFeeService类中所有build开头方法的COMMA_STR拼接分析

## 方法列表及COMMA_STR拼接行索引

### 1. buildCmSpCdr (行483-582)
**COMMA_STR拼接行及索引 (从0开始):**
- 索引0: 521行 `cBuf.append(m_sourceType).append(COMMA_STR);`
- 索引1: 522行 `cBuf.append(t_callType).append(COMMA_STR);`
- 索引2: 523行 `cBuf.append(String.valueOf(t_chSeqNum)).append(COMMA_STR);`
- 索引3: 524行 `cBuf.append("").append(COMMA_STR);`
- 索引4: 525行 `cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR);`
- 索引5: 526行 `cBuf.append("").append(COMMA_STR);`
- 索引6: 527行 `cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);`
- 索引7: 528行 `cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);`
- 索引8: 529行 `cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR);`
- 索引9: 530行 `cBuf.append(strStartTime).append(COMMA_STR);`
- 索引10: 531行 `cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR);`
- 索引11: 532行 `cBuf.append(strStartTime).append(COMMA_STR);`
- 索引12: 533行 `cBuf.append(0).append(COMMA_STR);`
- 索引13: 534行 `cBuf.append(0).append(COMMA_STR);`
- 索引14: 535行 `cBuf.append(0).append(COMMA_STR);`
- 索引15: 536行 `cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR);`
- 索引16: 537行 `cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR);`
- 索引17: 538行 `cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR);`
- 索引18: 539行 `cBuf.append("").append(COMMA_STR);`
- 索引19: 540行 `cBuf.append("").append(COMMA_STR);`
- 索引20: 541行 `cBuf.append("00001").append(COMMA_STR);`
- 索引21: 542行 `cBuf.append("").append(COMMA_STR);`
- 索引22: 543行 `cBuf.append("").append(COMMA_STR);`
- 索引23: 544行 `cBuf.append("").append(COMMA_STR);`
- 索引24: 545行 `cBuf.append("250").append(COMMA_STR);`
- 索引25: 546行 `cBuf.append("").append(COMMA_STR);`
- 索引26: 547行 `cBuf.append("").append(COMMA_STR);`
- 索引27: 548行 `appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT);` (内部可能有COMMA_STR)
- 索引28: 549行 `cBuf.append("").append(COMMA_STR);`
- 索引29: 550行 `cBuf.append("03").append(COMMA_STR);`
- 索引30: 551行 `cBuf.append("").append(COMMA_STR);`
- 索引31: 552行 `cBuf.append("").append(COMMA_STR);`
- 索引32: 553行 `cBuf.append("0").append(COMMA_STR);`
- 索引33: 554行 `cBuf.append("").append(COMMA_STR);`
- 索引34: 555行 `cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR);`
- 索引35: 556行 `cBuf.append("").append(COMMA_STR);`
- 索引36: 557行 `cBuf.append("").append(COMMA_STR);`
- 索引37: 558行 `cBuf.append("").append(COMMA_STR);`
- 索引38: 559行 `cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);`
- 索引39: 560行 `cBuf.append("").append(COMMA_STR);`
- 索引40: 561行 `cBuf.append("").append(COMMA_STR);`
- 索引41: 562行 `cBuf.append("").append(COMMA_STR);`
- 索引42: 563行 `cBuf.append("").append(COMMA_STR);`
- 索引43: 564行 `cBuf.append("").append(COMMA_STR);`
- 索引44: 565行 `cBuf.append("").append(COMMA_STR);`
- 索引45: 566行 `cBuf.append("").append(COMMA_STR);`
- 索引46: 567行 `cBuf.append("").append(COMMA_STR);`
- 索引47: 568行 `cBuf.append("").append(COMMA_STR);`
- 索引48: 569行 `cBuf.append("").append(COMMA_STR);`
- 索引49: 570行 `cBuf.append("").append(COMMA_STR);`
- 索引50: 571行 `cBuf.append("").append(COMMA_STR);`
- 索引51: 572行 `cBuf.append("").append(COMMA_STR);`
- 索引52: 573行 `cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR);`
- 索引53: 574行 `cBuf.append("").append(COMMA_STR);`
- 索引54: 575行 `cBuf.append("").append(COMMA_STR);`
- 索引55: 576行 `cBuf.append(m_sFileNo).append(COMMA_STR);`
- 索引56: 577行 `cBuf.append("000").append(COMMA_STR);`
- 索引57: 578行 `cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR);`
- 索引58: 579行 `cBuf.append(m_sAuditInfo).append(COMMA_STR);`

### 2. buildMmsSpCdr (行597-688)
**COMMA_STR拼接行及索引 (从0开始):**
- 索引0: 627行 `cBuf.append(m_sourceType).append(COMMA_STR);`
- 索引1: 628行 `cBuf.append(t_callType).append(COMMA_STR);`
- 索引2: 629行 `cBuf.append(t_chSeqNum).append(COMMA_STR);`
- 索引3: 630行 `cBuf.append("").append(COMMA_STR);`
- 索引4: 631行 `cBuf.append(threadLocalMapList.get("IMSI")).append(COMMA_STR);`
- 索引5: 632行 `cBuf.append("").append(COMMA_STR);`
- 索引6: 633行 `cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);`
- 索引7: 634行 `cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);`
- 索引8: 635行 `cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR);`
- 索引9: 636行 `cBuf.append(strStartTime).append(COMMA_STR);`
- 索引10: 637行 `cBuf.append(dbResultInfo.getT_chSysDate()).append(COMMA_STR);`
- 索引11: 638行 `cBuf.append(strStartTime).append(COMMA_STR);`
- 索引12: 639行 `cBuf.append("0").append(COMMA_STR);`
- 索引13: 640行 `cBuf.append("01").append(COMMA_STR);`
- 索引14: 641行 `cBuf.append("0").append(COMMA_STR);`
- 索引15: 642行 `cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR);`
- 索引16: 643行 `cBuf.append(dtOneUserSPlist.getsSpBizID()).append(COMMA_STR);`
- 索引17: 644行 `cBuf.append(threadLocalspInfo.getStrServCode()).append(COMMA_STR);`
- 索引18: 645行 `cBuf.append("").append(COMMA_STR);`
- 索引19: 646行 `cBuf.append("").append(COMMA_STR);`
- 索引20: 647行 `cBuf.append("").append(COMMA_STR);`
- 索引21: 648行 `cBuf.append("").append(COMMA_STR);`
- 索引22: 649行 `cBuf.append("0").append(COMMA_STR);`
- 索引23: 650行 `cBuf.append(Montnetconfig.m_strDealTime.get().substring(4, Montnetconfig.m_strDealTime.get().length())).append(COMMA_STR);`
- 索引24: 651行 `cBuf.append("250").append(COMMA_STR);`
- 索引25: 652行 `cBuf.append("").append(COMMA_STR);`
- 索引26: 653行 `cBuf.append("").append(COMMA_STR);`
- 索引27: 654行 `appendInstanceId(cBuf, dtOneUserSPlist, mcdrChargeCfgDT);` (内部可能有COMMA_STR)
- 索引28: 655行 `cBuf.append("00").append(COMMA_STR);`
- 索引29: 656行 `cBuf.append("02").append(COMMA_STR);`
- 索引30: 657行 `cBuf.append("3").append(COMMA_STR);`
- 索引31: 658行 `cBuf.append("02").append(COMMA_STR);`
- 索引32: 659行 `cBuf.append("0").append(COMMA_STR);`
- 索引33: 660行 `cBuf.append("").append(COMMA_STR);`
- 索引34: 661行 `cBuf.append(threadLocaloperInfo.getnFEE()).append(COMMA_STR);`
- 索引35: 662行 `cBuf.append("").append(COMMA_STR);`
- 索引36: 663行 `cBuf.append("").append(COMMA_STR);`
- 索引37: 664行 `cBuf.append("").append(COMMA_STR);`
- 索引38: 665行 `cBuf.append("").append(COMMA_STR);`
- 索引39: 666行 `cBuf.append("").append(COMMA_STR);`
- 索引40: 667行 `cBuf.append("999001").append(COMMA_STR);`
- 索引41: 668行 `cBuf.append("999001").append(COMMA_STR);`
- 索引42: 669行 `cBuf.append("0").append(COMMA_STR);`
- 索引43: 670行 `cBuf.append("0").append(COMMA_STR);`
- 索引44: 671行 `cBuf.append("00000").append(COMMA_STR);`
- 索引45: 672行 `cBuf.append("1").append(COMMA_STR);`
- 索引46: 673行 `cBuf.append("0").append(COMMA_STR);`
- 索引47: 674行 `cBuf.append("0").append(COMMA_STR);`
- 索引48: 675行 `cBuf.append("0").append(COMMA_STR);`
- 索引49: 676行 `cBuf.append(dtOneUserSPlist.getsSpID()).append(COMMA_STR);`
- 索引50: 677行 `cBuf.append("").append(COMMA_STR);`
- 索引51: 678行 `cBuf.append(threadLocalMapList.get("SERVICE_NUMBER")).append(COMMA_STR);`
- 索引52: 679行 `cBuf.append(dtOneUserSPlist.getnBillFlag()).append(COMMA_STR);`
- 索引53: 680行 `cBuf.append("").append(COMMA_STR);`
- 索引54: 681行 `cBuf.append("").append(COMMA_STR);`
- 索引55: 682行 `cBuf.append(m_sFileNo).append(COMMA_STR);`
- 索引56: 683行 `cBuf.append("000").append(COMMA_STR);`
- 索引57: 684行 `cBuf.append(Montnetconfig.m_strDealTime.get()).append(COMMA_STR);`
- 索引58: 685行 `cBuf.append(m_sAuditInfo).append(COMMA_STR);`

### 3. buildWapSpCdr (行690-782)
### 4. buildFlashSpCdr (行784-869)
### 5. buildStmSpCdr (行871-948)
### 6. buildMobileMapSpCdr (行950-1027)
### 7. buildMobileMallSpCdr (行1029-1105)
### 8. buildMobileReadSpCdr (行1107-1183)
### 9. buildHangDoubleSpCdr (行1185-1275)
### 10. buildMobileDaohangSpCdr (行1277-1354)
### 11. buildMobileGameSpCdr (行1356-1439)
### 12. buildMagicHeSpCdr (行1441-1516)
### 13. buildMiguSpCdr (行1518-1594)
### 14. buildEduHeSpCdr (行1596-1673)
### 15. buildMusicSpCdr (行1675-1751)
### 16. buildUnknowSpCdr (行1753-1829)
### 17. buildLianMingSpCdr (行1831-1908)
### 18. buildOtherTypeSpCdr (行1910-1985)
### 19. buildMobileBagSpCdr (行1987-2064)
### 20. buildNewGpSpCdr (行2066及以后)

**总计发现20个以build开头的方法**
