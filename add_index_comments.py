#!/usr/bin/env python3
"""
脚本用于为MonthFeeService.java中的build方法添加COMMA_STR索引注释
"""

import re
import sys

def process_java_file(file_path):
    """处理Java文件，为build方法中的COMMA_STR拼接行添加索引注释"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # 找到所有build方法的开始行
    build_methods = []
    for i, line in enumerate(lines):
        if re.search(r'public void build[A-Za-z]*\(', line):
            build_methods.append(i)
    
    print(f"找到 {len(build_methods)} 个build方法")
    
    # 处理每个build方法
    for method_start in build_methods:
        method_name = extract_method_name(lines[method_start])
        print(f"处理方法: {method_name}")
        
        # 找到方法结束位置
        method_end = find_method_end(lines, method_start)
        
        # 在方法范围内查找并处理COMMA_STR拼接行
        index = 0
        for i in range(method_start, method_end):
            line = lines[i].strip()
            
            # 检查是否是cBuf.append(...).append(COMMA_STR)的行
            if ('cBuf.append(' in line and 
                '.append(COMMA_STR)' in line and 
                not line.endswith(f'// 索引{index}') and
                '// 索引' not in line):
                
                # 添加索引注释
                if line.endswith(';'):
                    lines[i] = lines[i].rstrip(';') + f'; // 索引{index}'
                else:
                    lines[i] = lines[i] + f' // 索引{index}'
                index += 1
            
            # 检查appendInstanceId调用
            elif ('appendInstanceId(' in line and 
                  not line.endswith(f'// 索引{index}') and
                  '// 索引' not in line):
                lines[i] = lines[i] + f' // 索引{index}'
                index += 1
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))
    
    print("处理完成!")

def extract_method_name(line):
    """从方法声明行提取方法名"""
    match = re.search(r'public void (build[A-Za-z]*)\(', line)
    return match.group(1) if match else "unknown"

def find_method_end(lines, start):
    """找到方法的结束位置"""
    brace_count = 0
    found_first_brace = False
    
    for i in range(start, len(lines)):
        line = lines[i]
        
        # 计算大括号
        for char in line:
            if char == '{':
                brace_count += 1
                found_first_brace = True
            elif char == '}':
                brace_count -= 1
                
                # 如果找到了第一个开括号，且括号计数回到0，说明方法结束
                if found_first_brace and brace_count == 0:
                    return i + 1
    
    return len(lines)

if __name__ == "__main__":
    file_path = "provider/src/main/java/com/cl/provider/impl/MonthFeeService.java"
    process_java_file(file_path)
